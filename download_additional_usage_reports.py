#!/usr/bin/env python3
"""Download additional app usage related reports"""

import os
import sys
import gzip

# Add the dags/dependencies directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'dags', 'dependencies'))

from apple.apple_api_client import AppleAnalyticsClient
from apple.apple_config import REPORT_PROCESSING_CONFIG

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

def download_additional_reports():
    """Download additional app usage related reports"""
    
    print("🍎 Initializing Apple Analytics client...")
    client = AppleAnalyticsClient()
    
    # Additional reports to download
    target_reports = {
        "App Runtime Usage": "r167-3bfe16a0-ded2-444c-82fb-ec819d301f01",
        "App Store Installation and Deletion Standard": "r6-3bfe16a0-ded2-444c-82fb-ec819d301f01",
        "App Store Installation and Deletion Detailed": "r7-3bfe16a0-ded2-444c-82fb-ec819d301f01",
        "App Install Performance": "r5-3bfe16a0-ded2-444c-82fb-ec819d301f01",
        "App Downloads Standard": "r3-3bfe16a0-ded2-444c-82fb-ec819d301f01",
        "App Downloads Detailed": "r4-3bfe16a0-ded2-444c-82fb-ec819d301f01"
    }
    
    # Output directory
    output_dir = "app_usage_reports"
    os.makedirs(output_dir, exist_ok=True)
    print(f"📁 Using output directory: {output_dir}")
    
    try:
        # Download each report
        for report_name, report_id in target_reports.items():
            print(f"\n{'='*60}")
            print(f"📥 Processing: {report_name}")
            print(f"   Report ID: {report_id}")
            
            try:
                # Get the most recent instance
                instance_id = client.get_instances(report_id)
                
                # Get segment URL
                segment_url = client.get_segments(instance_id)
                print(f"🔗 Got segment URL")
                
                # Download the data
                print(f"⬇️  Downloading report data...")
                raw_data = client.download_segment(segment_url)
                
                # Create safe filename
                safe_filename = report_name.lower().replace(" ", "_").replace("/", "_")
                compressed_path = os.path.join(output_dir, f"{safe_filename}.gz")
                
                # Save compressed file
                with open(compressed_path, 'wb') as f:
                    f.write(raw_data)
                print(f"💾 Saved compressed file: {compressed_path}")
                
                # Try to decompress and save as CSV
                try:
                    csv_content = gzip.decompress(raw_data).decode('utf-8')
                    csv_path = os.path.join(output_dir, f"{safe_filename}.csv")
                    
                    with open(csv_path, 'w') as f:
                        f.write(csv_content)
                    
                    print(f"📄 Saved CSV file: {csv_path}")
                    
                    # Show preview
                    lines = csv_content.split('\n')
                    print(f"📊 File contains {len(lines)} lines")
                    
                    # Show headers
                    if lines:
                        headers = lines[0].split('\t')
                        print(f"\n📋 Columns ({len(headers)}):")
                        for i, header in enumerate(headers[:10]):
                            print(f"   {i+1}. {header}")
                        if len(headers) > 10:
                            print(f"   ... and {len(headers) - 10} more columns")
                    
                except Exception as e:
                    print(f"⚠️  Could not decompress as CSV: {e}")
                
            except Exception as e:
                print(f"❌ Error downloading {report_name}: {e}")
                continue
        
        print(f"\n{'='*60}")
        print(f"✅ Download complete!")
        
        # List all files in directory
        print(f"\n📄 All files in {output_dir}:")
        files = sorted([f for f in os.listdir(output_dir) if f.endswith('.csv')])
        for file in files:
            file_path = os.path.join(output_dir, file)
            size_mb = os.path.getsize(file_path) / (1024 * 1024)
            print(f"   - {file} ({size_mb:.2f} MB)")
        
    except Exception as e:
        print(f"\n❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    download_additional_reports()