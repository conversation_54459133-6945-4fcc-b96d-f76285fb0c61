#!/usr/bin/env python3
"""List all available reports to find App Usage related ones"""

import os
import sys

# Add the dags/dependencies directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'dags', 'dependencies'))

from apple.apple_api_client import AppleAnalyticsClient
from apple.apple_config import REPORT_PROCESSING_CONFIG

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

def list_all_reports():
    """List all available reports"""
    
    print("🍎 Initializing Apple Analytics client...")
    client = AppleAnalyticsClient()
    
    report_request_id = REPORT_PROCESSING_CONFIG["existing_report_request_id"]
    
    try:
        # Get all available reports
        print("\n📊 Fetching all available reports...")
        all_reports = client._make_request("GET", f"/analyticsReportRequests/{report_request_id}/reports")
        available_reports = all_reports.json().get("data", [])
        
        print(f"\n✅ Found {len(available_reports)} total reports:")
        
        # Group reports by category
        app_usage_reports = []
        app_store_reports = []
        crash_reports = []
        other_reports = []
        
        for report in available_reports:
            name = report.get("attributes", {}).get("name", "")
            report_id = report.get("id", "")
            
            if "App Usage" in name or "Sessions" in name or "CarPlay" in name or "Runtime" in name:
                app_usage_reports.append((name, report_id))
            elif "App Store" in name or "Downloads" in name or "Installation" in name or "Purchases" in name:
                app_store_reports.append((name, report_id))
            elif "Crash" in name:
                crash_reports.append((name, report_id))
            else:
                other_reports.append((name, report_id))
        
        print("\n📱 APP USAGE REPORTS:")
        for name, rid in sorted(app_usage_reports):
            print(f"  - {name} ({rid})")
            
        print("\n🏪 APP STORE REPORTS:")
        for name, rid in sorted(app_store_reports):
            print(f"  - {name} ({rid})")
            
        print("\n💥 CRASH REPORTS:")
        for name, rid in sorted(crash_reports):
            print(f"  - {name} ({rid})")
            
        print("\n📊 OTHER REPORTS:")
        for name, rid in sorted(other_reports)[:10]:  # Show first 10
            print(f"  - {name} ({rid})")
        
        if len(other_reports) > 10:
            print(f"  ... and {len(other_reports) - 10} more")
        
    except Exception as e:
        print(f"\n❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    list_all_reports()