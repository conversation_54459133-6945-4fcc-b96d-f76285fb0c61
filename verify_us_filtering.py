import csv
import re
from collections import defaultdict

def verify_us_filtering():
    """
    Comprehensive verification that we didn't miss any US merchants
    """
    print("Verifying US merchant filtering...")
    
    input_file = "/Users/<USER>/Documents/GitHub/phia/data-pipeline/affiliate_merchants_20250717_162247.csv"
    us_file = "/Users/<USER>/Documents/GitHub/phia/data-pipeline/affiliate_merchants_US_only_20250717_163402.csv"
    
    # Read original file
    all_merchants = []
    with open(input_file, 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        all_merchants = list(reader)
    
    # Read filtered US file
    us_merchants = []
    with open(us_file, 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        us_merchants = list(reader)
    
    print(f"Total merchants: {len(all_merchants):,}")
    print(f"US merchants found: {len(us_merchants):,}")
    
    # Create set of US merchant IDs for quick lookup
    us_merchant_ids = {m['id'] for m in us_merchants}
    
    # Extended US indicators - more comprehensive list
    us_indicators = [
        'US', 'USA', 'United States', 'America', 'American',
        'FlexOffers US', 'CJ US', 'Rakuten US', 'Impact US', 
        'AvantLink US', 'Pepperjam US', 'Awin US', 'ShareASale',
        'GoAffPro US', 'Webgains US'
    ]
    
    # Check for potentially missed US merchants
    potentially_missed = []
    non_us_networks = defaultdict(int)
    
    for merchant in all_merchants:
        merchant_id = merchant['id']
        source = merchant.get('source', '').strip()
        network = merchant.get('network', '').strip()
        name = merchant.get('name', '').strip()
        
        # If not in our US list, check if it might be US
        if merchant_id not in us_merchant_ids:
            # Check for any US indicators
            combined_text = f"{source} {network} {name}".upper()
            
            # Look for US patterns
            us_patterns = [
                r'\bUS\b', r'\bUSA\b', r'\bUNITED STATES\b', 
                r'\bAMERIC[A|AN]\b', r'\.COM\b'
            ]
            
            might_be_us = False
            for pattern in us_patterns:
                if re.search(pattern, combined_text):
                    might_be_us = True
                    break
            
            # Also check for common US-only companies/brands
            us_brands = [
                'WALMART', 'TARGET', 'AMAZON', 'NORDSTROM', 'MACY',
                'BEST BUY', 'HOME DEPOT', 'COSTCO', 'SEARS'
            ]
            
            for brand in us_brands:
                if brand in combined_text:
                    might_be_us = True
                    break
            
            if might_be_us:
                potentially_missed.append({
                    'id': merchant_id,
                    'name': name,
                    'source': source,
                    'network': network
                })
            else:
                # Track non-US networks
                non_us_networks[network] += 1
    
    # Show potentially missed merchants
    if potentially_missed:
        print(f"\n⚠️  Potentially missed US merchants ({len(potentially_missed)}):")
        for merchant in potentially_missed[:20]:  # Show first 20
            print(f"  {merchant['name']:<40} - {merchant['network']:<30}")
        if len(potentially_missed) > 20:
            print(f"  ... and {len(potentially_missed) - 20} more")
    else:
        print("\n✅ No potentially missed US merchants found!")
    
    # Show top non-US networks to verify they're actually non-US
    print(f"\nTop 15 Non-US networks (verification):")
    sorted_networks = sorted(non_us_networks.items(), key=lambda x: x[1], reverse=True)
    for network, count in sorted_networks[:15]:
        print(f"  {network:<40} - {count:4d} merchants")
    
    # Check for edge cases in our US merchants
    print(f"\nUS Networks found in filtered data:")
    us_networks = defaultdict(int)
    for merchant in us_merchants:
        network = merchant.get('network', 'Unknown')
        us_networks[network] += 1
    
    sorted_us_networks = sorted(us_networks.items(), key=lambda x: x[1], reverse=True)
    for network, count in sorted_us_networks:
        print(f"  {network:<40} - {count:4d} merchants")
    
    # Double-check: look for merchants with .com domains that might be US
    print(f"\nChecking .com domains not in US list...")
    com_domains_missed = []
    for merchant in all_merchants:
        if merchant['id'] not in us_merchant_ids:
            name = merchant.get('name', '').lower()
            if '.com' in name and not any(country in merchant.get('network', '').lower() 
                                        for country in ['uk', 'germany', 'france', 'canada', 'australia']):
                com_domains_missed.append(merchant)
    
    if com_domains_missed:
        print(f"Found {len(com_domains_missed)} .com domains not in US list:")
        for merchant in com_domains_missed[:10]:
            print(f"  {merchant['name']:<40} - {merchant['network']:<30}")
    else:
        print("No suspicious .com domains found outside US list.")
    
    return len(potentially_missed)

if __name__ == "__main__":
    missed_count = verify_us_filtering()
    if missed_count == 0:
        print(f"\n✅ Verification complete: No US merchants appear to have been missed!")
    else:
        print(f"\n⚠️  Found {missed_count} potentially missed merchants - review recommended")
