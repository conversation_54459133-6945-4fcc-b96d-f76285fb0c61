#!/usr/bin/env python3
"""Download all available App Usage reports from Apple Analytics"""

import os
import sys
import gzip
from datetime import datetime

# Add the dags/dependencies directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'dags', 'dependencies'))

from apple.apple_api_client import AppleAnalyticsClient
from apple.apple_config import REPORT_PROCESSING_CONFIG

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

def download_app_usage_reports():
    """Download all App Usage related reports"""
    
    print("🍎 Initializing Apple Analytics client...")
    client = AppleAnalyticsClient()
    
    # Reports to search for (based on your list)
    target_report_keywords = [
        "App Usage",
        "App Clip Usage", 
        "App Crashes",
        "App Store Installations and Deletions",
        "App Store Opt-in",
        "App Sessions",
        "CarPlay App Usage",
        "Platform App Installs"
    ]
    
    # Use the existing report request ID from config
    report_request_id = REPORT_PROCESSING_CONFIG["existing_report_request_id"]
    print(f"📋 Using report request ID: {report_request_id}")
    
    # Create output directory
    output_dir = "app_usage_reports"
    os.makedirs(output_dir, exist_ok=True)
    print(f"📁 Created output directory: {output_dir}")
    
    try:
        # Get all available reports
        print("\n📊 Fetching all available reports...")
        all_reports = client._make_request("GET", f"/analyticsReportRequests/{report_request_id}/reports")
        available_reports = all_reports.json().get("data", [])
        
        print(f"✅ Found {len(available_reports)} total reports available")
        
        # Find matching reports
        matching_reports = []
        for report in available_reports:
            report_name = report.get("attributes", {}).get("name", "")
            # Check if any of our keywords are in the report name
            for keyword in target_report_keywords:
                if keyword.lower() in report_name.lower():
                    matching_reports.append(report)
                    break
        
        print(f"\n🔍 Found {len(matching_reports)} matching app usage reports:")
        for report in matching_reports:
            print(f"  - {report.get('attributes', {}).get('name')}")
        
        # Download each matching report
        downloaded_count = 0
        failed_count = 0
        
        for report in matching_reports:
            report_name = report.get("attributes", {}).get("name", "Unknown")
            report_id = report["id"]
            
            print(f"\n{'='*60}")
            print(f"📥 Processing: {report_name}")
            print(f"   Report ID: {report_id}")
            
            try:
                # Get the most recent instance
                instance_id = client.get_instances(report_id)
                
                # Get segment URL
                segment_url = client.get_segments(instance_id)
                print(f"🔗 Got segment URL")
                
                # Download the data
                print(f"⬇️  Downloading report data...")
                raw_data = client.download_segment(segment_url)
                
                # Create safe filename
                safe_filename = report_name.lower().replace(" ", "_").replace("/", "_").replace(":", "")
                compressed_path = os.path.join(output_dir, f"{safe_filename}.gz")
                
                # Save compressed file
                with open(compressed_path, 'wb') as f:
                    f.write(raw_data)
                print(f"💾 Saved compressed file: {compressed_path}")
                
                # Try to decompress and save as CSV
                try:
                    csv_content = gzip.decompress(raw_data).decode('utf-8')
                    csv_path = os.path.join(output_dir, f"{safe_filename}.csv")
                    
                    with open(csv_path, 'w') as f:
                        f.write(csv_content)
                    
                    print(f"📄 Saved CSV file: {csv_path}")
                    
                    # Show file size and preview
                    lines = csv_content.split('\n')
                    print(f"📊 File contains {len(lines)} lines")
                    
                    # Show headers
                    if lines:
                        print(f"\n📋 Headers:")
                        print(f"   {lines[0][:150]}...")
                    
                    downloaded_count += 1
                    
                except Exception as e:
                    print(f"⚠️  Could not decompress as CSV: {e}")
                    downloaded_count += 1  # Still count as downloaded
                
            except Exception as e:
                print(f"❌ Error downloading {report_name}: {e}")
                failed_count += 1
                continue
        
        print(f"\n{'='*60}")
        print(f"✅ Download complete!")
        print(f"   Successfully downloaded: {downloaded_count} reports")
        print(f"   Failed: {failed_count} reports")
        print(f"\n📁 All files saved in: {output_dir}/")
        print(f"You can open the CSV files in Excel or any spreadsheet application.")
        
        # List all downloaded files
        print(f"\n📄 Downloaded files:")
        for file in sorted(os.listdir(output_dir)):
            if file.endswith('.csv'):
                file_path = os.path.join(output_dir, file)
                size_mb = os.path.getsize(file_path) / (1024 * 1024)
                print(f"   - {file} ({size_mb:.2f} MB)")
        
    except Exception as e:
        print(f"\n❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    download_app_usage_reports()