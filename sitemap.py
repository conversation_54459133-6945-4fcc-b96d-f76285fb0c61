"""
Bulletproof Sitemap Parser for E-commerce Product URLs
Handles 7000+ domains with maximum reliability and comprehensive fallbacks
"""

import requests
import xml.etree.ElementTree as ET
import pandas as pd
import csv
import time
import logging
from urllib.parse import urlparse, urljoin
from concurrent.futures import Thr<PERSON><PERSON>oolExecutor, as_completed
from typing import List, Dict, Optional, Tuple, Set
import re
from datetime import datetime
import gzip
from io import BytesIO
import json
from collections import defaultdict
import os

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[logging.FileHandler("sitemap_parsing.log"), logging.StreamHandler()],
)
logger = logging.getLogger(__name__)


class BulletproofSitemapParser:
    """Comprehensive sitemap parser with maximum success rate"""

    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update(
            {
                "User-Agent": "Mozilla/5.0 (compatible; SitemapBot/1.0; +http://example.com/bot)"
            }
        )
        self.session.timeout = 10

        # Comprehensive sitemap URL patterns
        self.sitemap_patterns = [
            # Standard sitemaps
            "/sitemap.xml",
            "/sitemap_index.xml",
            "/sitemapindex.xml",
            "/sitemap.xml.gz",
            # Product-specific sitemaps
            "/product-sitemap.xml",
            "/products-sitemap.xml",
            "/sitemap-products.xml",
            "/sitemap_products.xml",
            "/sitemap/products.xml",
            "/sitemaps/products.xml",
            # Platform-specific patterns
            # Shopify
            "/sitemap_products_1.xml",
            "/sitemap_products_2.xml",
            "/sitemap_collections_1.xml",
            # WooCommerce
            "/wp-sitemap.xml",
            "/wp-sitemap-posts-product-1.xml",
            "/product-sitemap.xml",
            "/sitemap-tax-product_cat.xml",
            # Magento
            "/sitemap/catalog/sitemap.xml",
            "/pub/sitemap.xml",
            "/media/sitemap/sitemap.xml",
            # BigCommerce
            "/xmlsitemap.php",
            "/sitemap_index.xml",
            # PrestaShop
            "/1_index_sitemap.xml",
            "/sitemap-products.xml",
            # Other platforms
            "/site-map.xml",
            "/sitemaps.xml",
            "/sitemap/sitemap.xml",
            "/sitemap-index.xml",
            "/sitemap_en.xml",
            "/sitemap-en.xml",
            "/sitemap1.xml",
            "/catalog/sitemap.xml",
        ]

        # Product URL indicators (comprehensive list)
        self.product_indicators = [
            "/product/",
            "/products/",
            "/item/",
            "/items/",
            "/p/",
            "/pd/",
            "/prod/",
            "/artikel/",
            "/produkt/",
            "/shop/",
            "/store/",
            "/catalog/",
            "/goods/",
            "/merchandise/",
            "/collection/",
            "/collections/",
            "/buy/",
            "/detail/",
            "/view/",
            "/-p-",
            "/-i-",
            "/dp/",
            "/gp/product/",
            "/itm/",
            "/listing/",
        ]

        # Patterns that indicate NOT a product page
        self.exclude_patterns = [
            "/category/",
            "/categories/",
            "/collections/",
            "/all",
            "/search",
            "/filter",
            "/sort",
            "/page/",
            "/tag/",
            "/blog/",
            "/about",
            "/contact",
            "/cart",
            "/checkout",
            "/account",
            "/login",
            "/register",
            "/wishlist",
            "/terms",
            "/privacy",
            "/shipping",
            "/returns",
            "/size-guide",
            "/help",
            "/faq",
            "/sitemap",
        ]

        # E-commerce platform signatures
        self.platform_signatures = {
            "shopify": ["shopify", "myshopify.com", "/cart/add", "shopify-assets"],
            "woocommerce": ["woocommerce", "wp-content", "/product/", "add-to-cart"],
            "magento": ["magento", "/catalog/product/", "mage/", "skin/frontend"],
            "bigcommerce": ["bigcommerce", "__bc-sf-filter", "bigcommerce.com"],
            "prestashop": ["prestashop", "id_product", "/product.php"],
            "squarespace": ["squarespace", "sqsp", "/commerce/"],
            "wix": ["wix", "_wix", "/product-page/"],
            "opencart": ["opencart", "route=product/product", "product_id="],
            "volusion": ["volusion", "/ProductDetails.asp", "-p/"],
            "ecwid": ["ecwid", "ec-store", "/shop/p/"],
        }

        self.stats = {
            "total_processed": 0,
            "successful": 0,
            "partial": 0,
            "failed": 0,
            "platform_breakdown": defaultdict(int),
        }

    def detect_platform(self, domain: str, content: str = None) -> Optional[str]:
        """Detect e-commerce platform from domain or content"""
        if content:
            content_lower = content.lower()
            for platform, signatures in self.platform_signatures.items():
                if any(sig in content_lower for sig in signatures):
                    return platform

        # Check domain patterns
        if "myshopify.com" in domain:
            return "shopify"

        return None

    def get_robots_txt_sitemaps(self, domain: str) -> List[str]:
        """Extract sitemap URLs from robots.txt"""
        sitemaps = []
        try:
            response = self.session.get(f"https://{domain}/robots.txt", timeout=5)
            if response.status_code == 200:
                for line in response.text.split("\n"):
                    if line.strip().lower().startswith("sitemap:"):
                        sitemap_url = line.split(":", 1)[1].strip()
                        if sitemap_url.startswith("http"):
                            sitemaps.append(sitemap_url)
                        else:
                            sitemaps.append(urljoin(f"https://{domain}", sitemap_url))
        except Exception as e:
            logger.debug(f"robots.txt error for {domain}: {e}")

        return sitemaps

    def parse_sitemap_content(
        self, content: bytes, base_url: str
    ) -> Tuple[List[str], List[str]]:
        """Parse sitemap content and return (product_urls, sub_sitemaps)"""
        product_urls = []
        sub_sitemaps = []

        try:
            # Handle gzipped content
            if content[:2] == b"\x1f\x8b":
                content = gzip.decompress(content)

            # Parse XML
            root = ET.fromstring(content)

            # Handle different sitemap namespaces
            namespaces = {
                "": "http://www.sitemaps.org/schemas/sitemap/0.9",
                "xsi": "http://www.w3.org/2001/XMLSchema-instance",
                "image": "http://www.google.com/schemas/sitemap-image/1.1",
            }

            # Check if this is a sitemap index
            if root.tag.endswith("sitemapindex"):
                # Extract sub-sitemaps
                for sitemap in root.findall(
                    ".//{http://www.sitemaps.org/schemas/sitemap/0.9}sitemap"
                ):
                    loc = sitemap.find(
                        "{http://www.sitemaps.org/schemas/sitemap/0.9}loc"
                    )
                    if loc is not None and loc.text:
                        # Prioritize product-related sitemaps
                        if any(
                            term in loc.text.lower()
                            for term in ["product", "item", "shop", "catalog"]
                        ):
                            sub_sitemaps.insert(0, loc.text)
                        else:
                            sub_sitemaps.append(loc.text)
            else:
                # Extract URLs from urlset
                for url in root.findall(
                    ".//{http://www.sitemaps.org/schemas/sitemap/0.9}url"
                ):
                    loc = url.find("{http://www.sitemaps.org/schemas/sitemap/0.9}loc")
                    if loc is not None and loc.text:
                        if self.is_product_url(loc.text):
                            product_urls.append(loc.text)

        except ET.ParseError as e:
            logger.debug(f"XML parse error: {e}")
            # Try to extract URLs using regex as fallback
            try:
                text = content.decode("utf-8", errors="ignore")
                urls = re.findall(r"<loc>(https?://[^<]+)</loc>", text)
                for url in urls:
                    if self.is_product_url(url):
                        product_urls.append(url)
                    elif "sitemap" in url.lower():
                        sub_sitemaps.append(url)
            except:
                pass

        return product_urls, sub_sitemaps

    def is_product_url(self, url: str) -> bool:
        """Enhanced product URL detection"""
        url_lower = url.lower()

        # Exclude non-product pages
        if any(exclude in url_lower for exclude in self.exclude_patterns):
            return False

        # Check for product indicators
        has_product_indicator = any(
            indicator in url_lower for indicator in self.product_indicators
        )

        # Additional heuristics
        has_product_id = bool(
            re.search(r"[-/](\d{3,}|[a-f0-9]{8,})", url)
        )  # Numeric or hex ID
        has_sku_pattern = bool(re.search(r"[A-Z]{2,}-?\d{3,}", url))  # SKU patterns
        ends_with_product_ext = url.endswith((".html", ".htm", ".php", ".asp", ".aspx"))
        has_slug_pattern = bool(
            re.search(r"/[\w-]+-[\w-]+-[\w-]+/?$", url)
        )  # product-name-style

        # Platform-specific patterns
        is_shopify_product = "/products/" in url_lower and url.count("/") >= 4
        is_woo_product = "/product/" in url_lower and not url_lower.endswith(
            "/product/"
        )

        return (
            has_product_indicator
            or has_product_id
            or has_sku_pattern
            or ends_with_product_ext
            or has_slug_pattern
            or is_shopify_product
            or is_woo_product
        )

    def extract_products_from_domain(self, domain: str, max_products: int = 2) -> Dict:
        """Extract product URLs from a single domain using all sitemap strategies"""
        result = {
            "domain": domain,
            "products": [],
            "pattern": None,
            "platform": None,
            "status": "pending",
            "method": None,
            "checked_sitemaps": 0,
            "error": None,
        }

        try:
            # Clean domain
            domain = (
                domain.strip()
                .lower()
                .replace("http://", "")
                .replace("https://", "")
                .strip("/")
            )

            # Track all sitemap URLs to check
            sitemap_urls = []

            # 1. Get sitemaps from robots.txt
            robots_sitemaps = self.get_robots_txt_sitemaps(domain)
            sitemap_urls.extend(robots_sitemaps)

            # 2. Try standard sitemap locations
            for pattern in self.sitemap_patterns:
                sitemap_urls.append(f"https://{domain}{pattern}")

            # 3. Try with www subdomain if not present
            if not domain.startswith("www."):
                for pattern in self.sitemap_patterns[:5]:  # Just try main patterns
                    sitemap_urls.append(f"https://www.{domain}{pattern}")

            # Remove duplicates while preserving order
            seen = set()
            unique_sitemaps = []
            for url in sitemap_urls:
                if url not in seen:
                    seen.add(url)
                    unique_sitemaps.append(url)

            # Process sitemaps
            all_product_urls = []
            sub_sitemaps_to_check = []
            platform_detected = None

            for sitemap_url in unique_sitemaps:
                if (
                    len(all_product_urls) >= max_products * 2
                ):  # Get extra for better pattern detection
                    break

                try:
                    response = self.session.get(sitemap_url, timeout=8)
                    result["checked_sitemaps"] += 1

                    if response.status_code == 200:
                        # Detect platform from response
                        if not platform_detected:
                            platform_detected = self.detect_platform(
                                domain, response.text
                            )
                            if platform_detected:
                                result["platform"] = platform_detected

                        # Parse sitemap
                        products, sub_sitemaps = self.parse_sitemap_content(
                            response.content, sitemap_url
                        )

                        all_product_urls.extend(products)
                        sub_sitemaps_to_check.extend(sub_sitemaps)

                        if products:
                            result["method"] = f"sitemap: {urlparse(sitemap_url).path}"
                            logger.info(
                                f"Found {len(products)} products in {sitemap_url}"
                            )

                except requests.exceptions.RequestException as e:
                    logger.debug(f"Request error for {sitemap_url}: {e}")
                    continue

            # Process sub-sitemaps if needed
            if len(all_product_urls) < max_products and sub_sitemaps_to_check:
                for sub_sitemap in sub_sitemaps_to_check[:5]:  # Limit sub-sitemaps
                    if len(all_product_urls) >= max_products * 2:
                        break

                    try:
                        response = self.session.get(sub_sitemap, timeout=8)
                        result["checked_sitemaps"] += 1

                        if response.status_code == 200:
                            products, _ = self.parse_sitemap_content(
                                response.content, sub_sitemap
                            )
                            all_product_urls.extend(products)

                            if products and not result["method"]:
                                result["method"] = (
                                    f"sub-sitemap: {urlparse(sub_sitemap).path}"
                                )

                    except:
                        continue

            # Deduplicate and select best products
            unique_products = list(dict.fromkeys(all_product_urls))  # Preserve order

            # Prioritize products with better indicators
            scored_products = []
            for url in unique_products:
                score = 0
                url_lower = url.lower()

                # Score based on URL quality
                if "/products/" in url_lower:
                    score += 3
                if re.search(r"-\d{3,}", url):  # Has ID
                    score += 2
                if url.count("/") >= 4:  # Deeper URLs often = products
                    score += 1
                if any(term in url_lower for term in ["new", "sale", "best"]):
                    score += 1

                scored_products.append((score, url))

            # Sort by score and take best products
            scored_products.sort(key=lambda x: x[0], reverse=True)
            result["products"] = [url for _, url in scored_products[:max_products]]

            # Extract URL pattern if we have products
            if len(result["products"]) >= 2:
                result["pattern"] = self.extract_url_pattern(domain, result["products"])
                result["status"] = "success"
            elif len(result["products"]) == 1:
                result["status"] = "partial"
            else:
                result["status"] = "failed"

        except Exception as e:
            logger.error(f"Error processing {domain}: {e}")
            result["status"] = "error"
            result["error"] = str(e)

        return result

    def extract_url_pattern(self, domain: str, urls: List[str]) -> Optional[str]:
        """Extract common URL pattern from product URLs"""
        if len(urls) < 2:
            return None

        try:
            # Parse URLs
            parsed_urls = [urlparse(url) for url in urls]
            paths = [p.path.strip("/").split("/") for p in parsed_urls]

            # Find common prefix
            common_segments = []
            min_length = min(len(p) for p in paths)

            for i in range(min_length):
                if all(p[i] == paths[0][i] for p in paths if len(p) > i):
                    common_segments.append(paths[0][i])
                else:
                    break

            # Build pattern
            if common_segments:
                pattern = f"https://{domain}/{'/'.join(common_segments)}"
                if not pattern.endswith("/"):
                    pattern += "/"
                return pattern

        except Exception as e:
            logger.debug(f"Pattern extraction error: {e}")

        return None

    def process_all_domains(
        self,
        csv_path: str,
        output_path: str = None,
        max_workers: int = 10,
        batch_size: int = 100,
    ):
        """Process all domains from CSV file"""

        # Load domains
        logger.info(f"Loading domains from {csv_path}")
        df = pd.read_csv(csv_path)

        # Extract and clean domains
        domains = []
        for _, row in df.iterrows():
            domain = str(row.get("domain", "")).strip().lower()
            domain = domain.replace("https://", "").replace("http://", "").strip("/")
            if domain and domain not in domains:  # Remove duplicates
                domains.append(
                    {
                        "domain": domain,
                        "name": str(row.get("name", "")),
                        "on_shopmy": row.get("On ShopMy?"),
                        "niche": row.get("niche_classification"),
                    }
                )

        logger.info(f"Found {len(domains)} unique domains to process")

        # Set output path
        if output_path is None:
            output_path = (
                f'sitemap_products_{datetime.now().strftime("%Y%m%d_%H%M%S")}.csv'
            )

        # Process in batches
        all_results = []
        checkpoint_file = output_path.replace(".csv", "_checkpoint.json")

        # Load checkpoint if exists
        processed_domains = set()
        if os.path.exists(checkpoint_file):
            with open(checkpoint_file, "r") as f:
                checkpoint_data = json.load(f)
                processed_domains = set(checkpoint_data.get("processed", []))
                all_results = checkpoint_data.get("results", [])
                logger.info(
                    f"Resuming from checkpoint: {len(processed_domains)} already processed"
                )

        # Filter out already processed domains
        remaining_domains = [d for d in domains if d["domain"] not in processed_domains]

        # Process batches
        for batch_start in range(0, len(remaining_domains), batch_size):
            batch_end = min(batch_start + batch_size, len(remaining_domains))
            batch = remaining_domains[batch_start:batch_end]

            logger.info(
                f"Processing batch {batch_start//batch_size + 1}: domains {batch_start + 1} to {batch_end}"
            )

            # Process batch with thread pool
            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                future_to_domain = {
                    executor.submit(self.extract_products_from_domain, d["domain"]): d
                    for d in batch
                }

                for future in as_completed(future_to_domain):
                    domain_info = future_to_domain[future]
                    try:
                        result = future.result()
                        # Add merchant info
                        result["merchant_name"] = domain_info["name"]
                        result["on_shopmy"] = domain_info["on_shopmy"]
                        result["niche"] = domain_info["niche"]

                        all_results.append(result)
                        processed_domains.add(domain_info["domain"])

                        # Update stats
                        self.stats["total_processed"] += 1
                        if result["status"] == "success":
                            self.stats["successful"] += 1
                        elif result["status"] == "partial":
                            self.stats["partial"] += 1
                        else:
                            self.stats["failed"] += 1

                        if result["platform"]:
                            self.stats["platform_breakdown"][result["platform"]] += 1

                        # Log progress
                        if self.stats["total_processed"] % 50 == 0:
                            success_rate = (
                                self.stats["successful"] / self.stats["total_processed"]
                            ) * 100
                            logger.info(
                                f"Progress: {self.stats['total_processed']} processed, "
                                f"{success_rate:.1f}% success rate"
                            )

                    except Exception as e:
                        logger.error(f"Error processing {domain_info['domain']}: {e}")

            # Save checkpoint after each batch
            self._save_checkpoint(checkpoint_file, list(processed_domains), all_results)

            # Save intermediate results
            self._save_results(all_results, output_path)

            # Small delay between batches
            time.sleep(1)

        # Final save
        self._save_results(all_results, output_path)
        self._print_final_summary(all_results)

        # Clean up checkpoint
        if os.path.exists(checkpoint_file):
            os.remove(checkpoint_file)

        logger.info(f"Processing complete! Results saved to {output_path}")

    def _save_checkpoint(
        self, checkpoint_file: str, processed: List[str], results: List[Dict]
    ):
        """Save checkpoint for resuming"""
        with open(checkpoint_file, "w") as f:
            json.dump(
                {
                    "processed": processed,
                    "results": results,
                    "timestamp": datetime.now().isoformat(),
                },
                f,
            )

    def _save_results(self, results: List[Dict], output_path: str):
        """Save results to CSV in the requested format"""
        # Save main detailed results
        detailed_output = output_path.replace(".csv", "_detailed.csv")
        with open(detailed_output, "w", newline="", encoding="utf-8") as csvfile:
            fieldnames = [
                "merchant_name",
                "domain",
                "product_url_1",
                "product_url_2",
                "url_pattern",
                "status",
                "platform",
                "method",
                "checked_sitemaps",
                "on_shopmy",
                "niche",
            ]
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()

            for result in results:
                row = {
                    "merchant_name": result.get("merchant_name", ""),
                    "domain": result["domain"],
                    "product_url_1": (
                        result["products"][0] if len(result["products"]) > 0 else ""
                    ),
                    "product_url_2": (
                        result["products"][1] if len(result["products"]) > 1 else ""
                    ),
                    "url_pattern": result["pattern"] or "",
                    "status": result["status"],
                    "platform": result["platform"] or "",
                    "method": result["method"] or "",
                    "checked_sitemaps": result["checked_sitemaps"],
                    "on_shopmy": result.get("on_shopmy", ""),
                    "niche": result.get("niche", ""),
                }
                writer.writerow(row)

        # Save simplified regex format as requested
        with open(output_path, "w", newline="", encoding="utf-8") as csvfile:
            fieldnames = ["domain", "product_catalog_regex"]
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()

            for result in results:
                if result["status"] == "success" and result["pattern"]:
                    # Convert pattern to regex format
                    regex_pattern = self._convert_to_regex_pattern(
                        result["domain"], result["pattern"]
                    )
                    row = {
                        "domain": result["domain"],
                        "product_catalog_regex": regex_pattern,
                    }
                    writer.writerow(row)

    def _convert_to_regex_pattern(self, domain: str, pattern: str) -> str:
        """Convert URL pattern to regex format as requested"""
        # Extract the path part from the pattern
        parsed = urlparse(pattern)
        path = parsed.path.rstrip("/")

        if path:
            # Create regex pattern
            # Example: "classicsixny\\.com.*/products/.*"
            escaped_domain = domain.replace(".", "\\.")
            regex = f"{escaped_domain}.*{path}/.*"
        else:
            # If no specific path, just match the domain with anything after
            escaped_domain = domain.replace(".", "\\.")
            regex = f"{escaped_domain}.*/.*"

        return regex

    def _print_final_summary(self, results: List[Dict]):
        """Print comprehensive summary"""
        total = len(results)
        successful = sum(1 for r in results if r["status"] == "success")
        partial = sum(1 for r in results if r["status"] == "partial")
        failed = sum(1 for r in results if r["status"] == "failed")

        print("\n" + "=" * 70)
        print("SITEMAP PARSING COMPLETE")
        print("=" * 70)
        print(f"Total domains processed: {total}")
        print(f"Successful (2+ products): {successful} ({successful/total*100:.1f}%)")
        print(f"Partial (1 product): {partial} ({partial/total*100:.1f}%)")
        print(f"Failed (0 products): {failed} ({failed/total*100:.1f}%)")

        # Platform breakdown
        platform_stats = defaultdict(int)
        for r in results:
            if r["platform"]:
                platform_stats[r["platform"]] += 1

        print("\n" + "=" * 70)
        print("PLATFORM BREAKDOWN")
        print("=" * 70)
        for platform, count in sorted(
            platform_stats.items(), key=lambda x: x[1], reverse=True
        ):
            print(f"{platform.capitalize()}: {count} ({count/total*100:.1f}%)")

        # Method effectiveness
        method_stats = defaultdict(int)
        for r in results:
            if r["method"] and r["status"] == "success":
                method_type = r["method"].split(":")[0]
                method_stats[method_type] += 1

        print("\n" + "=" * 70)
        print("SUCCESSFUL EXTRACTION METHODS")
        print("=" * 70)
        for method, count in sorted(
            method_stats.items(), key=lambda x: x[1], reverse=True
        ):
            print(f"{method}: {count} successes")

        # Average sitemaps checked
        avg_checked = sum(r["checked_sitemaps"] for r in results) / total
        print(f"\nAverage sitemaps checked per domain: {avg_checked:.1f}")

        # Show sample regex patterns
        print("\n" + "=" * 70)
        print("SAMPLE REGEX PATTERNS GENERATED")
        print("=" * 70)
        sample_count = 0
        for r in results:
            if r["status"] == "success" and r["pattern"] and sample_count < 5:
                regex = self._convert_to_regex_pattern(r["domain"], r["pattern"])
                print(f"{r['domain']:<30} → {regex}")
                sample_count += 1


# Main execution
if __name__ == "__main__":
    # Configuration
    CSV_FILE = "AllShopMyMerchants7125_FASHION_AND_APPAREL.csv"
    OUTPUT_FILE = "sitemap_products_results.csv"

    # Initialize parser
    parser = BulletproofSitemapParser()

    # Process all domains
    # Use max_workers=20 for faster processing (adjust based on your system)
    parser.process_all_domains(
        csv_path=CSV_FILE,
        output_path=OUTPUT_FILE,
        max_workers=20,  # Concurrent requests
        batch_size=100,  # Process in batches of 100
    )

    # For testing with a smaller subset:
    # parser.process_all_domains(
    #     csv_path=CSV_FILE,
    #     output_path="test_results.csv",
    #     max_workers=5,
    #     batch_size=10
    # )
