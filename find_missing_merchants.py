import csv
import re
from difflib import SequenceMatcher


def normalize_merchant_name(name):
    """
    Normalize merchant names for better matching
    """
    if not name:
        return ""

    # Convert to lowercase and remove extra spaces
    name = name.lower().strip()

    # Remove common suffixes and prefixes
    name = re.sub(r"\s*(inc|llc|ltd|corp|corporation|company|co\.?)\s*$", "", name)
    name = re.sub(r"^\s*(the)\s+", "", name)

    # Remove special characters and extra spaces
    name = re.sub(r"[^\w\s]", " ", name)
    name = re.sub(r"\s+", " ", name).strip()

    # Remove common words that might cause confusion
    name = re.sub(r"\s+(us|usa|america|american)\s*$", "", name)

    return name


def similarity(a, b):
    """
    Calculate similarity between two strings
    """
    return SequenceMatcher(None, a, b).ratio()


def find_missing_merchants():
    """
    Find merchants in US affiliate database that are missing from current merchants
    """
    print("Finding missing merchants...")

    current_merchants_file = (
        "/Users/<USER>/Documents/GitHub/phia/data-pipeline/download (19).csv"
    )
    us_merchants_file = "/Users/<USER>/Documents/GitHub/phia/data-pipeline/affiliate_merchants_US_final_20250717_163734.csv"
    output_file = "missing.csv"

    # Read current merchants (ones we're in contact with)
    print("Reading current merchants...")
    current_merchants = set()
    current_merchants_list = []

    with open(current_merchants_file, "r", encoding="utf-8") as f:
        reader = csv.DictReader(f)
        for row in reader:
            name = row.get("advertiser_name", "").strip()
            if name:
                normalized_name = normalize_merchant_name(name)
                current_merchants.add(normalized_name)
                current_merchants_list.append(
                    {"original_name": name, "normalized_name": normalized_name}
                )

    print(f"Found {len(current_merchants)} current merchants")

    # Read US affiliate merchants
    print("Reading US affiliate merchants...")
    us_merchants = []

    with open(us_merchants_file, "r", encoding="utf-8") as f:
        reader = csv.DictReader(f)
        for row in reader:
            name = row.get("name", "").strip()
            if name:
                normalized_name = normalize_merchant_name(name)
                us_merchants.append(
                    {
                        "id": row.get("id", ""),
                        "name": name,
                        "normalized_name": normalized_name,
                        "network": row.get("network", ""),
                        "source": row.get("source", ""),
                        "product_count": row.get("product_count", ""),
                        "logo": row.get("logo", ""),
                    }
                )

    print(f"Found {len(us_merchants)} US affiliate merchants")

    # Find missing merchants
    print("Comparing merchants...")
    missing_merchants = []
    similarity_threshold = 0.85  # Adjust this to be more or less strict

    for us_merchant in us_merchants:
        us_name = us_merchant["normalized_name"]

        # Check for exact match first
        if us_name in current_merchants:
            continue

        # Check for similar matches
        is_similar = False
        for current_merchant in current_merchants_list:
            current_name = current_merchant["normalized_name"]

            # Calculate similarity
            sim_score = similarity(us_name, current_name)

            if sim_score >= similarity_threshold:
                is_similar = True
                break

        # If no similar match found, it's missing
        if not is_similar:
            missing_merchants.append(us_merchant)

    # Sort missing merchants by product count (descending)
    missing_merchants.sort(
        key=lambda x: int(x["product_count"].replace(",", "") or "0"), reverse=True
    )

    # Write missing merchants to CSV
    print(f"Writing {len(missing_merchants)} missing merchants to {output_file}...")

    with open(output_file, "w", newline="", encoding="utf-8") as f:
        fieldnames = [
            "id",
            "name",
            "network",
            "source",
            "product_count",
            "logo",
            "potential_revenue_tier",
            "priority_score",
        ]
        writer = csv.DictWriter(f, fieldnames=fieldnames)
        writer.writeheader()

        for merchant in missing_merchants:
            # Calculate priority score based on product count
            product_count = int(merchant["product_count"].replace(",", "") or "0")

            if product_count >= 10000:
                tier = "High"
                priority = 5
            elif product_count >= 1000:
                tier = "Medium-High"
                priority = 4
            elif product_count >= 100:
                tier = "Medium"
                priority = 3
            elif product_count >= 10:
                tier = "Low-Medium"
                priority = 2
            else:
                tier = "Low"
                priority = 1

            writer.writerow(
                {
                    "id": merchant["id"],
                    "name": merchant["name"],
                    "network": merchant["network"],
                    "source": merchant["source"],
                    "product_count": merchant["product_count"],
                    "logo": merchant["logo"],
                    "potential_revenue_tier": tier,
                    "priority_score": priority,
                }
            )

    print(f"Successfully created {output_file}")

    # Print summary statistics
    print(f"\nSummary:")
    print(f"Current merchants: {len(current_merchants):,}")
    print(f"US affiliate merchants: {len(us_merchants):,}")
    print(f"Missing merchants: {len(missing_merchants):,}")
    print(
        f"Coverage: {((len(us_merchants) - len(missing_merchants)) / len(us_merchants) * 100):.1f}%"
    )

    # Show top missing merchants by product count
    print(f"\nTop 10 missing merchants by product count:")
    for i, merchant in enumerate(missing_merchants[:10]):
        print(
            f"  {i+1:2d}. {merchant['name']:<40} - {merchant['product_count']:>10} products ({merchant['network']})"
        )

    # Show distribution by network
    print(f"\nMissing merchants by network:")
    network_counts = {}
    for merchant in missing_merchants:
        network = merchant["network"]
        network_counts[network] = network_counts.get(network, 0) + 1

    sorted_networks = sorted(network_counts.items(), key=lambda x: x[1], reverse=True)
    for network, count in sorted_networks[:10]:
        print(f"  {network:<40} - {count:4d} merchants")

    # Show priority distribution
    print(f"\nPriority distribution:")
    priority_counts = {}
    for merchant in missing_merchants:
        product_count = int(merchant["product_count"].replace(",", "") or "0")
        if product_count >= 10000:
            tier = "High (10k+ products)"
        elif product_count >= 1000:
            tier = "Medium-High (1k-10k products)"
        elif product_count >= 100:
            tier = "Medium (100-1k products)"
        elif product_count >= 10:
            tier = "Low-Medium (10-100 products)"
        else:
            tier = "Low (<10 products)"

        priority_counts[tier] = priority_counts.get(tier, 0) + 1

    for tier, count in priority_counts.items():
        print(f"  {tier:<30} - {count:4d} merchants")


if __name__ == "__main__":
    find_missing_merchants()
    print(f"\n✅ Missing merchants analysis complete! Check 'missing.csv' for results.")
