# ShopMy Niche Classification Script

This script processes the `all_shopmy_initial_set.csv` file and classifies each row into a specific niche using OpenAI's GPT-4o-mini model. It processes 100 requests in parallel to expedite the classification process.

## Features

- **Parallel Processing**: Processes 100 API calls concurrently for maximum speed
- **Error Handling**: Comprehensive error handling with retry logic and exponential backoff
- **Progress Tracking**: Real-time progress updates with ETA and processing rate
- **Checkpoint System**: Saves progress and can resume from interruptions
- **Graceful Shutdown**: Handles SIGINT/SIGTERM signals to save progress before exit
- **Rate Limit Handling**: Automatically handles OpenAI rate limits
- **Comprehensive Logging**: Detailed logs for debugging and monitoring

## Requirements

Install the required dependencies:

```bash
pip install -r requirements.txt
```

## Setup

1. **Set your OpenAI API Key**: 
   Edit `classify_shopmy_niches.py` and replace `YOUR_OPENAI_API_KEY_HERE` with your actual OpenAI API key:
   
   ```python
   OPENAI_API_KEY = "sk-your-actual-api-key-here"
   ```

2. **Verify Input File**: 
   Ensure `all_shopmy_initial_set.csv` is in the same directory as the script.

## Usage

### Test First (Recommended)

Before processing the full dataset, test with a small subset:

```bash
python test_classifier.py
```

This will test the classifier with 3 sample rows to ensure everything is working correctly.

### Run Full Classification

```bash
python classify_shopmy_niches.py
```

## Output Files

The script generates several output files:

- **`classified_shopmy_niches_YYYYMMDD_HHMMSS.csv`**: Main output file with original data + `niche_classification` column
- **`classification_errors_YYYYMMDD_HHMMSS.log`**: Detailed error logs
- **`classification_errors_YYYYMMDD_HHMMSS.csv`**: CSV file with rows that failed classification
- **`classification_checkpoint_YYYYMMDD_HHMMSS.json`**: Checkpoint file for resuming interrupted runs

## Configuration

You can modify these settings in `classify_shopmy_niches.py`:

```python
MAX_CONCURRENT_REQUESTS = 100  # Number of parallel requests
MAX_RETRIES = 3               # Retry attempts for failed requests
RETRY_DELAY = 1               # Base delay between retries (seconds)
RATE_LIMIT_DELAY = 60         # Delay for rate limit errors (seconds)
MODEL = "gpt-4o-mini"         # OpenAI model to use
```

## Niche Categories

The script classifies brands into these categories:

- Fashion & Apparel
- Beauty & Skincare
- Health & Wellness
- Home & Garden
- Technology & Electronics
- Sports & Fitness
- Food & Beverage
- Travel & Lifestyle
- Baby & Kids
- Jewelry & Accessories
- Books & Education
- Art & Crafts
- Automotive
- Pet Supplies
- Other

## Performance

- **Processing Speed**: ~100 requests per minute (depending on OpenAI API response times)
- **Estimated Time**: ~8-10 hours for 45,988 rows
- **Memory Usage**: Low memory footprint due to batch processing

## Resuming Interrupted Runs

If the script is interrupted, it automatically saves a checkpoint. When you restart the script, it will:

1. Load the checkpoint file
2. Resume from where it left off
3. Continue processing remaining rows

## Troubleshooting

### Common Issues

1. **API Key Error**: Make sure you've set your OpenAI API key correctly
2. **Rate Limits**: The script handles rate limits automatically, but you may need to wait
3. **Network Issues**: The script will retry failed requests automatically
4. **Memory Issues**: The script processes in batches to avoid memory problems

### Monitoring Progress

The script provides real-time progress updates every 5 seconds:

```
Progress: 1500/45988 (3.3%) | Rate: 25.2 req/s | ETA: 29.4 min | Errors: 2
```

### Logs

Check the log files for detailed information about:
- Processing progress
- API errors
- Retry attempts
- Failed classifications

## Cost Estimation

With GPT-4o-mini pricing:
- ~45,988 requests
- ~50 tokens per request
- Estimated cost: ~$1-2 USD

## Support

If you encounter issues:

1. Check the error logs
2. Verify your API key and quota
3. Test with the test script first
4. Check OpenAI API status

## Files Structure

```
data-filtering/
├── all_shopmy_initial_set.csv          # Input file
├── classify_shopmy_niches.py           # Main classification script
├── test_classifier.py                  # Test script
├── requirements.txt                    # Python dependencies
├── README_classifier.md                # This file
└── Output files (generated):
    ├── classified_shopmy_niches_*.csv
    ├── classification_errors_*.log
    ├── classification_errors_*.csv
    └── classification_checkpoint_*.json
```
