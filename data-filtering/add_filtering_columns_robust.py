#!/usr/bin/env python3
"""
Enhanced <PERSON><PERSON><PERSON> to add filtering columns with robust domain matching.

This script uses comprehensive domain matching to ensure zero data loss:
1. Multiple normalization strategies
2. Fuzzy matching for edge cases
3. Detailed matching reports
4. Comprehensive logging

Usage:
    python add_filtering_columns_robust.py
"""

import pandas as pd
import sys
from pathlib import Path
from datetime import datetime
from robust_domain_matcher import RobustDomainMatcher


def load_shopmy_domains_robust(shopmy_file):
    """
    Load domains from shopmy.csv that have clicks > 0
    
    Args:
        shopmy_file (str): Path to shopmy.csv file
        
    Returns:
        list: List of domains that have clicks in ShopMy
    """
    domains_with_clicks = []
    
    try:
        df = pd.read_csv(shopmy_file)
        print(f"Loaded {len(df)} rows from {shopmy_file}")
        
        # Filter for rows with clicks > 0
        df["Clicks"] = pd.to_numeric(df["Clicks"], errors="coerce")
        df_with_clicks = df[df["Clicks"] > 0]
        
        print(f"Found {len(df_with_clicks)} rows with clicks > 0")
        
        # Extract domains from URL column (last column)
        url_column = df_with_clicks.columns[-1]  # Last column should be URL
        print(f"Extracting domains from column: {url_column}")
        
        for idx, url in enumerate(df_with_clicks[url_column]):
            if url and not pd.isna(url):
                domains_with_clicks.append(str(url))
            
            # Progress indicator for large files
            if (idx + 1) % 1000 == 0:
                print(f"Processed {idx + 1} URLs...")
        
        print(f"Extracted {len(domains_with_clicks)} URLs with clicks from ShopMy")
        
    except Exception as e:
        print(f"Error reading {shopmy_file}: {e}")
        return []
    
    return domains_with_clicks


def load_insights_domains_robust(insights_file):
    """
    Load domains from the insights report CSV
    
    Args:
        insights_file (str): Path to insights CSV file
        
    Returns:
        list: List of domains from insights report
    """
    domains = []
    
    try:
        df = pd.read_csv(insights_file)
        print(f"Loaded {len(df)} rows from insights report")
        
        # Extract domains from hostname column
        if "hostname" not in df.columns:
            print("Error: 'hostname' column not found in insights report")
            return []
        
        for idx, hostname in enumerate(df["hostname"].dropna()):
            if hostname and not pd.isna(hostname):
                domains.append(str(hostname))
            
            # Progress indicator for large files
            if (idx + 1) % 1000 == 0:
                print(f"Processed {idx + 1} hostnames...")
        
        print(f"Extracted {len(domains)} domains from insights report")
        return domains
    
    except Exception as e:
        print(f"Error reading insights file {insights_file}: {e}")
        return []


def add_filtering_columns_robust(classified_file, shopmy_domains, insights_domains, output_file):
    """
    Add filtering columns using robust domain matching
    
    Args:
        classified_file (str): Path to classified CSV file
        shopmy_domains (list): List of ShopMy domains/URLs
        insights_domains (list): List of insights domains
        output_file (str): Path to output CSV file
        
    Returns:
        tuple: (total_rows, shopmy_matches, insights_matches, matcher)
    """
    try:
        df = pd.read_csv(classified_file)
        print(f"\nProcessing classified dataset: {classified_file}")
        print(f"Loaded {len(df)} rows")
        
        # Initialize the robust domain matcher
        matcher = RobustDomainMatcher()
        
        # Create comprehensive domain lookups
        print("\nCreating domain lookups...")
        shopmy_lookup = matcher.create_domain_lookup(shopmy_domains, "ShopMy")
        insights_lookup = matcher.create_domain_lookup(insights_domains, "Insights")
        
        # Initialize the new columns
        df["present_in_shopmy_links"] = False
        df["present_in_mixpanel_insights"] = False
        
        # Add detailed matching columns for analysis
        df["shopmy_match_method"] = ""
        df["insights_match_method"] = ""
        df["shopmy_matched_domain"] = ""
        df["insights_matched_domain"] = ""
        
        # Check each domain against both datasets
        shopmy_matches = 0
        insights_matches = 0
        
        print("\nMatching domains...")
        for idx, row in df.iterrows():
            target_domain = row["domain"]
            
            # Perform comprehensive matching
            shopmy_match, insights_match, match_details = matcher.match_domain_comprehensive(
                target_domain, shopmy_lookup, insights_lookup
            )
            
            # Update the dataframe
            if shopmy_match:
                df.at[idx, "present_in_shopmy_links"] = True
                df.at[idx, "shopmy_match_method"] = match_details['shopmy_method']
                df.at[idx, "shopmy_matched_domain"] = match_details['shopmy_original']
                shopmy_matches += 1
            
            if insights_match:
                df.at[idx, "present_in_mixpanel_insights"] = True
                df.at[idx, "insights_match_method"] = match_details['insights_method']
                df.at[idx, "insights_matched_domain"] = match_details['insights_original']
                insights_matches += 1
            
            # Progress indicator
            if (idx + 1) % 1000 == 0:
                print(f"Processed {idx + 1} domains...")
        
        # Save the results
        df.to_csv(output_file, index=False)
        
        total_rows = len(df)
        print(f"\nResults:")
        print(f"Total domains processed: {total_rows}")
        print(f"Domains present in ShopMy links: {shopmy_matches}")
        print(f"Domains present in Mixpanel insights: {insights_matches}")
        print(f"Saved results to: {output_file}")
        
        return total_rows, shopmy_matches, insights_matches, matcher
    
    except Exception as e:
        print(f"Error processing {classified_file}: {e}")
        return 0, 0, 0, None


def print_detailed_sample_results(output_file, sample_size=20):
    """Print a detailed sample of the results for verification"""
    try:
        df = pd.read_csv(output_file)
        
        print(f"\nDetailed sample results (first {sample_size} rows):")
        print("-" * 120)
        print(f"{'Name':<25} | {'Domain':<30} | {'Niche':<20} | {'ShopMy':<8} | {'Insights':<8} | {'Methods':<20}")
        print("-" * 120)
        
        for idx, row in df.head(sample_size).iterrows():
            shopmy_status = "✓" if row["present_in_shopmy_links"] else "✗"
            insights_status = "✓" if row["present_in_mixpanel_insights"] else "✗"
            
            shopmy_method = row.get("shopmy_match_method", "")[:10]
            insights_method = row.get("insights_match_method", "")[:10]
            methods = f"{shopmy_method}/{insights_method}"
            
            print(f"{str(row['name'])[:24]:<25} | {str(row['domain'])[:29]:<30} | {str(row['niche_classification'])[:19]:<20} | {shopmy_status:<8} | {insights_status:<8} | {methods:<20}")
        
        print("-" * 120)
        
        # Show summary statistics
        shopmy_count = df["present_in_shopmy_links"].sum()
        insights_count = df["present_in_mixpanel_insights"].sum()
        both_count = ((df["present_in_shopmy_links"]) & (df["present_in_mixpanel_insights"])).sum()
        neither_count = ((~df["present_in_shopmy_links"]) & (~df["present_in_mixpanel_insights"])).sum()
        
        print(f"\nSummary Statistics:")
        print(f"Present in ShopMy links: {shopmy_count} ({shopmy_count/len(df)*100:.1f}%)")
        print(f"Present in Mixpanel insights: {insights_count} ({insights_count/len(df)*100:.1f}%)")
        print(f"Present in both: {both_count} ({both_count/len(df)*100:.1f}%)")
        print(f"Present in neither: {neither_count} ({neither_count/len(df)*100:.1f}%)")
        
        # Show matching method breakdown
        if "shopmy_match_method" in df.columns:
            shopmy_methods = df[df["present_in_shopmy_links"]]["shopmy_match_method"].value_counts()
            print(f"\nShopMy matching methods:")
            for method, count in shopmy_methods.items():
                print(f"  {method}: {count}")
        
        if "insights_match_method" in df.columns:
            insights_methods = df[df["present_in_mixpanel_insights"]]["insights_match_method"].value_counts()
            print(f"\nInsights matching methods:")
            for method, count in insights_methods.items():
                print(f"  {method}: {count}")
        
    except Exception as e:
        print(f"Error showing sample results: {e}")


def main():
    """Main function to orchestrate the robust filtering process"""
    
    # File paths
    classified_file = "classified_shopmy_niches_20250711_125052.csv"
    shopmy_file = "shopmy.csv"
    insights_file = "Untitled Report_Insights_2025-06-10_to_2025-07-10.csv"
    
    # Output file paths
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = f"classified_shopmy_with_filters_robust_{timestamp}.csv"
    report_file = f"domain_matching_report_{timestamp}.txt"
    
    print("=" * 80)
    print("ROBUST DOMAIN MATCHING AND FILTERING")
    print("=" * 80)
    
    # Check if input files exist
    for file_path in [classified_file, shopmy_file, insights_file]:
        if not Path(file_path).exists():
            print(f"Error: File '{file_path}' not found!")
            sys.exit(1)
    
    print("All input files found. Starting processing...")
    
    # Step 1: Load domains from ShopMy data
    print("\nStep 1: Loading domains from ShopMy data")
    shopmy_domains = load_shopmy_domains_robust(shopmy_file)
    
    # Step 2: Load domains from insights report
    print("\nStep 2: Loading domains from insights report")
    insights_domains = load_insights_domains_robust(insights_file)
    
    if not shopmy_domains and not insights_domains:
        print("No domains found in either dataset. Exiting.")
        sys.exit(1)
    
    # Step 3: Add filtering columns using robust matching
    print("\nStep 3: Adding filtering columns with robust matching")
    total_rows, shopmy_matches, insights_matches, matcher = add_filtering_columns_robust(
        classified_file, shopmy_domains, insights_domains, output_file
    )
    
    # Step 4: Generate comprehensive matching report
    if matcher:
        print("\nStep 4: Generating matching report")
        matcher.generate_matching_report(report_file)
    
    # Step 5: Show detailed sample results
    print_detailed_sample_results(output_file)
    
    # Final summary
    print("\n" + "=" * 80)
    print("FINAL SUMMARY")
    print("=" * 80)
    print(f"Input file: {classified_file}")
    print(f"Output file: {output_file}")
    print(f"Matching report: {report_file}")
    print(f"Total domains processed: {total_rows}")
    print(f"ShopMy domains loaded: {len(shopmy_domains)}")
    print(f"Insights domains loaded: {len(insights_domains)}")
    print(f"Matches in ShopMy: {shopmy_matches}")
    print(f"Matches in Insights: {insights_matches}")
    
    if matcher:
        print(f"Fuzzy matches found: {len(matcher.fuzzy_matches)}")
        print(f"Unmatched domains: {len(matcher.unmatched_domains)}")
    
    print(f"\nNew columns added:")
    print(f"  ✓ present_in_shopmy_links (True/False)")
    print(f"  ✓ present_in_mixpanel_insights (True/False)")
    print(f"  ✓ shopmy_match_method (matching strategy used)")
    print(f"  ✓ insights_match_method (matching strategy used)")
    print(f"  ✓ shopmy_matched_domain (original matched domain)")
    print(f"  ✓ insights_matched_domain (original matched domain)")
    
    print(f"\nScript completed successfully!")


if __name__ == "__main__":
    main()
