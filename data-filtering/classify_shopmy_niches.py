#!/usr/bin/env python3
"""
Parallel CSV Processing Script for ShopMy Niche Classification

This script processes the all_shopmy_initial_set.csv file and classifies each row
into a niche using OpenAI's GPT-4o-mini model. It processes 100 requests in parallel
to expedite the classification process.

Usage:
    python classify_shopmy_niches.py

Requirements:
    pip install openai aiohttp asyncio pandas tqdm
"""

import asyncio
import csv
import json
import logging
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Tuple

import aiohttp
import pandas as pd
from tqdm.asyncio import tqdm
import signal
import sys

# Configuration
OPENAI_API_KEY = "********************************************************************************************************************************************************************"  # Replace with your actual API key
INPUT_CSV_PATH = "all_shopmy_initial_set.csv"
OUTPUT_CSV_PATH = (
    f"classified_shopmy_niches_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
)
ERROR_LOG_PATH = f"classification_errors_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
CHECKPOINT_PATH = (
    f"classification_checkpoint_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
)

# OpenAI Configuration
OPENAI_API_URL = "https://api.openai.com/v1/chat/completions"
MODEL = "gpt-4o-mini"
MAX_CONCURRENT_REQUESTS = 25000
MAX_RETRIES = 3
RETRY_DELAY = 1  # seconds
RATE_LIMIT_DELAY = 60  # seconds for rate limit errors

# Classification prompt template
CLASSIFICATION_PROMPT = """
You are an expert at categorizing e-commerce brands and websites into specific niches.

Given the following information about a brand/website:
- Name: {name}
- Domain: {domain}
- Payout: {payout}% {rate_type}
- On ShopMy: {on_shopmy}

Please classify this brand/website into ONE specific niche category. Choose the most appropriate category from common e-commerce niches such as:
- Fashion & Apparel
- Beauty & Skincare
- Health & Wellness
- Home & Garden
- Technology & Electronics
- Sports & Fitness
- Food & Beverage
- Travel & Lifestyle
- Baby & Kids
- Jewelry & Accessories
- Books & Education
- Art & Crafts
- Automotive
- Pet Supplies
- Other

Respond with ONLY the niche category name, nothing else.
"""

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[logging.FileHandler(ERROR_LOG_PATH), logging.StreamHandler()],
)
logger = logging.getLogger(__name__)


class ShopMyClassifier:
    def __init__(self):
        self.session: Optional[aiohttp.ClientSession] = None
        self.semaphore = asyncio.Semaphore(MAX_CONCURRENT_REQUESTS)
        self.processed_count = 0
        self.error_count = 0
        self.results = []
        self.errors = []
        self.start_time = time.time()
        self.last_progress_update = time.time()
        self.total_rows = 0

        # Setup signal handlers for graceful shutdown
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)

    def signal_handler(self, signum, frame):
        """Handle shutdown signals gracefully."""
        logger.info(f"Received signal {signum}. Saving progress and shutting down...")
        self.save_checkpoint(self.results, 0)  # Save current progress
        sys.exit(0)

    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()

    def update_progress(self):
        """Update progress display with rate limiting."""
        current_time = time.time()
        if current_time - self.last_progress_update >= 5:  # Update every 5 seconds
            elapsed_time = current_time - self.start_time
            rate = self.processed_count / elapsed_time if elapsed_time > 0 else 0
            remaining = self.total_rows - self.processed_count
            eta = remaining / rate if rate > 0 else 0

            logger.info(
                f"Progress: {self.processed_count}/{self.total_rows} "
                f"({self.processed_count/self.total_rows*100:.1f}%) | "
                f"Rate: {rate:.1f} req/s | "
                f"ETA: {eta/60:.1f} min | "
                f"Errors: {self.error_count}"
            )
            self.last_progress_update = current_time

    def build_prompt(self, row: Dict) -> str:
        """Build classification prompt for a CSV row."""
        return CLASSIFICATION_PROMPT.format(
            name=row.get("name", "N/A"),
            domain=row.get("domain", "N/A"),
            payout=row.get("fullPayout", "N/A"),
            rate_type=row.get("rateType", "N/A"),
            on_shopmy=row.get("On ShopMy?", "N/A"),
            cookie_window=row.get("Cookie Window", "N/A"),
        )

    async def classify_single_row(self, row: Dict, row_index: int) -> Optional[Dict]:
        """Classify a single row using OpenAI API."""
        async with self.semaphore:
            for attempt in range(MAX_RETRIES):
                try:
                    prompt = self.build_prompt(row)

                    payload = {
                        "model": MODEL,
                        "messages": [
                            {
                                "role": "system",
                                "content": "You are an expert e-commerce niche classifier. Respond with only the niche category name.",
                            },
                            {"role": "user", "content": prompt},
                        ],
                        "max_tokens": 50,
                        "temperature": 0.1,
                    }

                    headers = {
                        "Authorization": f"Bearer {OPENAI_API_KEY}",
                        "Content-Type": "application/json",
                    }

                    async with self.session.post(
                        OPENAI_API_URL, json=payload, headers=headers
                    ) as response:
                        if response.status == 200:
                            result = await response.json()
                            niche = result["choices"][0]["message"]["content"].strip()

                            # Add classification to row data
                            classified_row = row.copy()
                            classified_row["niche_classification"] = niche
                            classified_row["row_index"] = row_index

                            self.processed_count += 1
                            self.update_progress()
                            return classified_row

                        elif response.status == 429:  # Rate limit
                            logger.warning(
                                f"Rate limit hit for row {row_index}, attempt {attempt + 1}"
                            )
                            await asyncio.sleep(RATE_LIMIT_DELAY)
                            continue

                        else:
                            error_text = await response.text()
                            logger.error(
                                f"API error for row {row_index}: {response.status} - {error_text}"
                            )

                except Exception as e:
                    logger.error(
                        f"Exception for row {row_index}, attempt {attempt + 1}: {str(e)}"
                    )

                if attempt < MAX_RETRIES - 1:
                    await asyncio.sleep(
                        RETRY_DELAY * (2**attempt)
                    )  # Exponential backoff

            # If all retries failed
            self.error_count += 1
            error_row = row.copy()
            error_row["row_index"] = row_index
            error_row["error"] = "Failed after all retries"
            self.errors.append(error_row)
            logger.error(
                f"Failed to classify row {row_index} after {MAX_RETRIES} attempts"
            )
            return None

    async def process_batch(self, rows: List[Tuple[Dict, int]]) -> List[Dict]:
        """Process a batch of rows concurrently."""
        tasks = [self.classify_single_row(row, idx) for row, idx in rows]
        results = await tqdm.gather(*tasks, desc="Processing batch")
        return [result for result in results if result is not None]

    def save_checkpoint(self, processed_results: List[Dict], current_batch: int):
        """Save current progress to checkpoint file."""
        checkpoint_data = {
            "processed_results": processed_results,
            "current_batch": current_batch,
            "processed_count": self.processed_count,
            "error_count": self.error_count,
            "timestamp": datetime.now().isoformat(),
        }

        with open(CHECKPOINT_PATH, "w") as f:
            json.dump(checkpoint_data, f, indent=2)

        logger.info(
            f"Checkpoint saved: {self.processed_count} processed, {self.error_count} errors"
        )

    def load_checkpoint(self) -> Tuple[List[Dict], int]:
        """Load progress from checkpoint file if it exists."""
        if Path(CHECKPOINT_PATH).exists():
            try:
                with open(CHECKPOINT_PATH, "r") as f:
                    checkpoint_data = json.load(f)

                self.processed_count = checkpoint_data.get("processed_count", 0)
                self.error_count = checkpoint_data.get("error_count", 0)

                logger.info(
                    f"Checkpoint loaded: resuming from batch {checkpoint_data.get('current_batch', 0)}"
                )
                return checkpoint_data.get(
                    "processed_results", []
                ), checkpoint_data.get("current_batch", 0)
            except Exception as e:
                logger.error(f"Failed to load checkpoint: {e}")

        return [], 0

    async def process_csv(self):
        """Main method to process the entire CSV file."""
        logger.info(f"Starting classification of {INPUT_CSV_PATH}")

        # Load checkpoint if exists
        processed_results, start_batch = self.load_checkpoint()

        # Read CSV file
        try:
            df = pd.read_csv(INPUT_CSV_PATH)
            self.total_rows = len(df)
            logger.info(f"Loaded {self.total_rows} rows from CSV")
        except Exception as e:
            logger.error(f"Failed to read CSV file: {e}")
            return

        # Convert to list of dictionaries with row indices
        rows_with_indices = [(row.to_dict(), idx) for idx, row in df.iterrows()]

        # Skip already processed batches
        batch_size = MAX_CONCURRENT_REQUESTS
        start_index = start_batch * batch_size
        remaining_rows = rows_with_indices[start_index:]

        if start_index > 0:
            logger.info(f"Resuming from row {start_index}")

        # Process in batches
        for batch_num, i in enumerate(
            range(0, len(remaining_rows), batch_size), start_batch
        ):
            batch = remaining_rows[i - start_index : i - start_index + batch_size]

            logger.info(
                f"Processing batch {batch_num + 1}, rows {i + 1}-{min(i + len(batch), self.total_rows)}"
            )

            batch_results = await self.process_batch(batch)
            processed_results.extend(batch_results)

            # Save checkpoint after each batch
            self.save_checkpoint(processed_results, batch_num + 1)

            # Progress update
            logger.info(
                f"Batch {batch_num + 1} complete. Total processed: {self.processed_count}, Errors: {self.error_count}"
            )

        # Save final results
        await self.save_results(processed_results)

        logger.info(
            f"Classification complete! Processed: {self.processed_count}, Errors: {self.error_count}"
        )

    async def save_results(self, results: List[Dict]):
        """Save results to output CSV file."""
        if not results:
            logger.warning("No results to save")
            return

        try:
            # Sort results by original row index to maintain order
            results.sort(key=lambda x: x.get("row_index", 0))

            # Remove row_index from final output
            for result in results:
                result.pop("row_index", None)

            # Save to CSV
            df_results = pd.DataFrame(results)
            df_results.to_csv(OUTPUT_CSV_PATH, index=False)

            logger.info(f"Results saved to {OUTPUT_CSV_PATH}")

            # Save errors if any
            if self.errors:
                error_df = pd.DataFrame(self.errors)
                error_csv_path = f"classification_errors_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
                error_df.to_csv(error_csv_path, index=False)
                logger.info(f"Errors saved to {error_csv_path}")

        except Exception as e:
            logger.error(f"Failed to save results: {e}")


async def main():
    """Main function to run the classification process."""
    if OPENAI_API_KEY == "YOUR_OPENAI_API_KEY_HERE":
        logger.error("Please set your OpenAI API key in the OPENAI_API_KEY variable")
        return

    if not Path(INPUT_CSV_PATH).exists():
        logger.error(f"Input CSV file not found: {INPUT_CSV_PATH}")
        return

    start_time = time.time()

    async with ShopMyClassifier() as classifier:
        await classifier.process_csv()

    end_time = time.time()
    logger.info(f"Total processing time: {end_time - start_time:.2f} seconds")


if __name__ == "__main__":
    asyncio.run(main())
