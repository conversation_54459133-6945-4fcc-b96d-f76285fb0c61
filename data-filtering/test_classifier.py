#!/usr/bin/env python3
"""
Test script for the ShopMy classifier with a small subset of data.

This script creates a small test CSV file and runs the classifier on it
to validate functionality before processing the full dataset.
"""

import asyncio
import pandas as pd
import tempfile
import os
from pathlib import Path

# Import the classifier
from classify_shopmy_niches import ShopMyClassifier, OPENAI_API_KEY

async def test_classifier():
    """Test the classifier with a small dataset."""
    
    # Check if API key is set
    if OPENAI_API_KEY == "YOUR_OPENAI_API_KEY_HERE":
        print("❌ Please set your OpenAI API key in classify_shopmy_niches.py")
        return False
    
    # Create test data
    test_data = [
        {
            'name': 'Nike',
            'domain': 'nike.com',
            'fullPayout': 5.0,
            'rateType': 'percentage',
            'On ShopMy?': 'Yes',
            'Cookie Window': 30
        },
        {
            'name': 'Sephora',
            'domain': 'sephora.com',
            'fullPayout': 8.0,
            'rateType': 'percentage',
            'On ShopMy?': 'Yes',
            'Cookie Window': 7
        },
        {
            'name': 'Amazon',
            'domain': 'amazon.com',
            'fullPayout': 3.0,
            'rateType': 'percentage',
            'On ShopMy?': 'No',
            'Cookie Window': 24
        }
    ]
    
    # Create temporary CSV file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
        df = pd.DataFrame(test_data)
        df.to_csv(f.name, index=False)
        test_csv_path = f.name
    
    try:
        print(f"🧪 Testing classifier with {len(test_data)} rows...")
        
        # Update the input path temporarily
        import classify_shopmy_niches
        original_input_path = classify_shopmy_niches.INPUT_CSV_PATH
        classify_shopmy_niches.INPUT_CSV_PATH = test_csv_path
        
        # Run classifier
        async with ShopMyClassifier() as classifier:
            classifier.total_rows = len(test_data)
            
            # Test individual row classification
            for i, row in enumerate(test_data):
                print(f"  Testing row {i+1}: {row['name']} ({row['domain']})")
                result = await classifier.classify_single_row(row, i)
                
                if result:
                    print(f"    ✅ Classified as: {result.get('niche_classification', 'Unknown')}")
                else:
                    print(f"    ❌ Classification failed")
                    return False
        
        print("✅ All test classifications successful!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        return False
        
    finally:
        # Cleanup
        if os.path.exists(test_csv_path):
            os.unlink(test_csv_path)
        
        # Restore original input path
        if 'classify_shopmy_niches' in locals():
            classify_shopmy_niches.INPUT_CSV_PATH = original_input_path

def main():
    """Run the test."""
    print("🚀 Starting ShopMy Classifier Test")
    print("=" * 50)
    
    success = asyncio.run(test_classifier())
    
    print("=" * 50)
    if success:
        print("✅ Test completed successfully!")
        print("You can now run the full classifier with: python classify_shopmy_niches.py")
    else:
        print("❌ Test failed. Please check the error messages above.")
    
    return success

if __name__ == "__main__":
    main()
