#!/usr/bin/env python3
"""
Test script for robust domain matching to ensure zero data loss.

This script tests various edge cases and domain formats to validate
the comprehensive matching approach.
"""

import pandas as pd
import tempfile
import os
from robust_domain_matcher import <PERSON>ust<PERSON><PERSON>in<PERSON>atch<PERSON>


def test_domain_variants():
    """Test domain variant extraction"""
    print("🧪 Testing domain variant extraction...")
    
    matcher = RobustDomainMatcher()
    
    test_cases = [
        # Basic domains
        ("nike.com", {"nike.com"}),
        ("www.nike.com", {"nike.com"}),
        ("https://nike.com", {"nike.com"}),
        ("https://www.nike.com/products", {"nike.com"}),
        
        # Shopify domains
        ("00736a-81.myshopify.com", {"00736a-81.myshopify.com"}),
        ("https://00736a-81.myshopify.com/products", {"00736a-81.myshopify.com"}),
        
        # Complex URLs
        ("https://www.madewell.com/p/womens/clothing/tees/regenerative-cotton-ribbed-scoopneck-tank/NW197/?ccode=WT0005", {"madewell.com"}),
        ("https://shop.lululemon.com/p/men-pants/Relaxed-Fit-Workout-Track-Pant/_/prod11750190?color=31382", {"lululemon.com"}),
        
        # Edge cases
        ("nike.com:8080", {"nike.com"}),
        ("ftp://nike.com", {"nike.com"}),
        ("", set()),
        (None, set()),
    ]
    
    for input_domain, expected_variants in test_cases:
        variants = matcher.extract_all_domain_variants(input_domain)
        
        # Check if expected variants are present
        missing = expected_variants - variants
        extra = variants - expected_variants
        
        if not missing and not extra:
            print(f"  ✅ '{input_domain}' -> {variants}")
        else:
            print(f"  ❌ '{input_domain}' -> {variants}")
            if missing:
                print(f"     Missing: {missing}")
            if extra:
                print(f"     Extra: {extra}")
    
    print("Domain variant extraction test completed.\n")


def test_comprehensive_matching():
    """Test comprehensive domain matching with realistic data"""
    print("🧪 Testing comprehensive domain matching...")
    
    # Create realistic test data based on actual file formats
    test_classified = [
        {"name": "Nike", "domain": "nike.com"},
        {"name": "Lululemon", "domain": "lululemon.com"},
        {"name": "Madewell", "domain": "madewell.com"},
        {"name": "Lancer Skincare", "domain": "00736a-81.myshopify.com"},
        {"name": "Unknown Brand", "domain": "unknownbrand.com"},
        {"name": "Test Typo", "domain": "nike.co"},  # Typo case
    ]
    
    # ShopMy URLs (realistic format)
    test_shopmy_urls = [
        "https://shop.lululemon.com/p/men-pants/Relaxed-Fit-Workout-Track-Pant/_/prod11750190?color=31382",
        "https://www.madewell.com/p/womens/clothing/tees/regenerative-cotton-ribbed-scoopneck-tank/NW197/?ccode=WT0005",
        "https://00736a-81.myshopify.com/products/skincare",
        "https://teststore.com/products",
    ]
    
    # Insights hostnames (realistic format)
    test_insights_hostnames = [
        "nike.com",
        "madewell.com",
        "sephora.com",
        "unknownbrand.com",
    ]
    
    # Initialize matcher
    matcher = RobustDomainMatcher()
    
    # Create lookups
    shopmy_lookup = matcher.create_domain_lookup(test_shopmy_urls, "ShopMy")
    insights_lookup = matcher.create_domain_lookup(test_insights_hostnames, "Insights")
    
    print(f"ShopMy lookup created: {len(shopmy_lookup)} variants")
    print(f"Insights lookup created: {len(insights_lookup)} variants")
    
    # Test matching
    print("\nTesting matches:")
    expected_results = {
        "nike.com": {"shopmy": False, "insights": True},  # Only in insights
        "lululemon.com": {"shopmy": True, "insights": False},  # Only in shopmy
        "madewell.com": {"shopmy": True, "insights": True},  # In both
        "00736a-81.myshopify.com": {"shopmy": True, "insights": False},  # Shopify domain
        "unknownbrand.com": {"shopmy": False, "insights": True},  # Only in insights
        "nike.co": {"shopmy": False, "insights": False},  # Should not match (different TLD)
    }
    
    all_correct = True
    for item in test_classified:
        domain = item["domain"]
        name = item["name"]
        
        shopmy_match, insights_match, details = matcher.match_domain_comprehensive(
            domain, shopmy_lookup, insights_lookup
        )
        
        expected = expected_results.get(domain, {"shopmy": False, "insights": False})
        
        shopmy_correct = shopmy_match == expected["shopmy"]
        insights_correct = insights_match == expected["insights"]
        
        if shopmy_correct and insights_correct:
            print(f"  ✅ {name} ({domain}): ShopMy={shopmy_match}, Insights={insights_match}")
            if details['shopmy_method']:
                print(f"     ShopMy method: {details['shopmy_method']}")
            if details['insights_method']:
                print(f"     Insights method: {details['insights_method']}")
        else:
            print(f"  ❌ {name} ({domain}): Expected ShopMy={expected['shopmy']}, Insights={expected['insights']}")
            print(f"     Got ShopMy={shopmy_match}, Insights={insights_match}")
            all_correct = False
    
    # Test fuzzy matching
    print(f"\nFuzzy matches found: {len(matcher.fuzzy_matches)}")
    for fuzzy in matcher.fuzzy_matches:
        print(f"  {fuzzy['target']} -> {fuzzy['matched']} (score: {fuzzy['score']:.2f})")
    
    print(f"Unmatched domains: {len(matcher.unmatched_domains)}")
    for unmatched in matcher.unmatched_domains:
        print(f"  {unmatched}")
    
    return all_correct


def test_edge_cases():
    """Test edge cases that could cause data loss"""
    print("🧪 Testing edge cases...")
    
    matcher = RobustDomainMatcher()
    
    edge_cases = [
        # URL encoding
        "https%3A//nike.com",
        # Multiple subdomains
        "shop.us.nike.com",
        # International domains
        "nike.co.uk",
        # IP addresses (should not match)
        "***********",
        # Invalid domains
        "not-a-domain",
        # Empty/null cases
        "",
        None,
        "   ",
        # Special characters
        "nike.com/path?param=value&other=123",
        # Port numbers
        "nike.com:443",
        "nike.com:8080/path",
    ]
    
    print("Testing edge case domain extraction:")
    for case in edge_cases:
        variants = matcher.extract_all_domain_variants(case)
        print(f"  '{case}' -> {variants}")
    
    print("Edge case testing completed.\n")


def main():
    """Run all robust matching tests"""
    print("🚀 Starting Robust Domain Matching Tests")
    print("=" * 60)
    
    # Test 1: Domain variant extraction
    test_domain_variants()
    
    # Test 2: Comprehensive matching
    success = test_comprehensive_matching()
    
    # Test 3: Edge cases
    test_edge_cases()
    
    print("=" * 60)
    if success:
        print("✅ All tests passed! The robust matching system is working correctly.")
        print("This approach should ensure zero data loss through:")
        print("  • Multiple domain normalization strategies")
        print("  • Comprehensive variant generation")
        print("  • Fuzzy matching for edge cases")
        print("  • Detailed logging and reporting")
        print("\nYou can now run: python add_filtering_columns_robust.py")
    else:
        print("❌ Some tests failed. Please review the matching logic.")
    
    return success


if __name__ == "__main__":
    main()
