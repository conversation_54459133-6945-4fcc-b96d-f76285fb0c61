#!/usr/bin/env python3
"""
Script to filter makeup and furniture domains against the insights report.

This script:
1. Loads domains from makeup_domains_no_clicks.csv and furniture_domains_no_clicks.csv
2. Loads domains from the insights report CSV
3. Filters out domains that already exist in the insights report
4. Returns only completely new domains that don't appear anywhere
"""

import pandas as pd
import sys
from pathlib import Path


def normalize_domain(domain):
    """
    Normalize domain for consistent matching

    Args:
        domain (str): Domain to normalize

    Returns:
        str: Normalized domain or None if invalid
    """
    if not domain or pd.isna(domain):
        return None

    domain = str(domain).lower().strip()

    # Remove www. prefix for normalization
    if domain.startswith("www."):
        domain = domain[4:]

    # Remove port numbers if present
    if ":" in domain:
        domain = domain.split(":")[0]

    return domain if domain else None


def load_insights_domains(insights_file):
    """
    Load domains from the insights report CSV

    Args:
        insights_file (str): Path to insights CSV file

    Returns:
        set: Set of normalized domains from insights report
    """
    try:
        df = pd.read_csv(insights_file)
        print(f"Loaded {len(df)} rows from insights report")

        # Extract domains from hostname column
        if "hostname" not in df.columns:
            print("Error: 'hostname' column not found in insights report")
            return set()

        domains = set()
        for hostname in df["hostname"].dropna():
            normalized = normalize_domain(hostname)
            if normalized:
                domains.add(normalized)

        print(f"Extracted {len(domains)} unique domains from insights report")
        return domains

    except Exception as e:
        print(f"Error reading insights file {insights_file}: {e}")
        return set()


def filter_domains_against_insights(
    input_file, insights_domains, output_file, category
):
    """
    Filter domains from input file against insights domains

    Args:
        input_file (str): Path to input CSV file (makeup or furniture no-clicks)
        insights_domains (set): Set of domains from insights report
        output_file (str): Path to output CSV file
        category (str): Category name for logging (makeup/furniture)

    Returns:
        tuple: (total_domains, new_domains, existing_domains)
    """
    try:
        df = pd.read_csv(input_file)
        print(f"\nProcessing {category} file: {input_file}")
        print(f"Loaded {len(df)} rows")

        # Normalize domains in the input file
        df["normalized_domain"] = df["domain"].apply(normalize_domain)

        # Filter out domains that exist in insights report
        df_new = df[~df["normalized_domain"].isin(insights_domains)]

        # Remove the temporary normalized_domain column
        df_new = df_new.drop("normalized_domain", axis=1)

        # Save filtered results
        df_new.to_csv(output_file, index=False)

        total_domains = len(df)
        new_domains = len(df_new)
        existing_domains = total_domains - new_domains

        print(f"Total {category} domains (no clicks): {total_domains}")
        print(f"New domains (not in insights): {new_domains}")
        print(f"Existing domains (in insights): {existing_domains}")
        print(f"Saved new domains to: {output_file}")

        return total_domains, new_domains, existing_domains

    except Exception as e:
        print(f"Error processing {input_file}: {e}")
        return 0, 0, 0


def print_sample_domains(domains, title, sample_size=10):
    """Print a sample of domains for verification"""
    print(
        f"\n{title} (showing {min(sample_size, len(domains))} out of {len(domains)}):"
    )
    for i, domain in enumerate(sorted(list(domains))[:sample_size]):
        print(f"  {i+1}. {domain}")
    if len(domains) > sample_size:
        print(f"  ... and {len(domains) - sample_size} more")


def show_filtered_examples(input_file, insights_domains, category, sample_size=5):
    """Show examples of domains that were filtered out"""
    try:
        df = pd.read_csv(input_file)
        df["normalized_domain"] = df["domain"].apply(normalize_domain)

        # Find domains that exist in insights
        existing_domains = df[df["normalized_domain"].isin(insights_domains)]

        if len(existing_domains) > 0:
            print(
                f"\nExamples of {category} domains filtered out (already in insights):"
            )
            for i, row in existing_domains.head(sample_size).iterrows():
                print(f"  - {row['name']}: {row['domain']}")
        else:
            print(f"\nNo {category} domains were filtered out (none found in insights)")

    except Exception as e:
        print(f"Error showing filtered examples: {e}")


def main():
    """Main function to orchestrate the filtering process"""

    # File paths
    insights_file = "Untitled Report_Insights_2025-06-10_to_2025-07-10.csv"
    fashion_input = "fashion_domains_no_clicks.csv"

    # Output file paths
    fashion_output = "fashion_domains_final_new.csv"

    print("=" * 70)
    print("DOMAIN FILTERING AGAINST INSIGHTS REPORT")
    print("=" * 70)

    # Check if input files exist
    for file_path in [insights_file, fashion_input]:
        if not Path(file_path).exists():
            print(f"Error: File '{file_path}' not found!")
            sys.exit(1)

    print("All input files found. Starting processing...")

    # Step 1: Load domains from insights report
    print("\nStep 1: Loading domains from insights report")
    insights_domains = load_insights_domains(insights_file)

    if not insights_domains:
        print("No domains found in insights report. Exiting.")
        sys.exit(1)

    # Print sample insights domains for verification
    print_sample_domains(insights_domains, "Sample domains from insights report")

    # Step 2: Filter fashion domains
    print("\nStep 2: Filtering fashion domains against insights")
    fashion_total, fashion_new, fashion_existing = filter_domains_against_insights(
        fashion_input, insights_domains, fashion_output, "fashion"
    )

    # Show examples of filtered fashion domains
    show_filtered_examples(fashion_input, insights_domains, "fashion")

    # Summary
    print("\n" + "=" * 70)
    print("FINAL SUMMARY")
    print("=" * 70)
    print(f"Insights report domains: {len(insights_domains)}")

    print(f"\nFashion domains:")
    print(f"  Started with (no clicks): {fashion_total}")
    print(f"  Final new domains: {fashion_new}")
    print(f"  Filtered out (in insights): {fashion_existing}")
    print(
        f"  Percentage new: {(fashion_new/fashion_total*100):.1f}%"
        if fashion_total > 0
        else "  Percentage new: 0%"
    )

    print(f"\nTotal new domains to explore: {fashion_new}")

    print(f"\nFinal output files created:")
    print(f"  {fashion_output} - {fashion_new} completely new fashion domains")
    print("\nThese domains have:")
    print("  ✓ Zero clicks in shopmy.csv")
    print("  ✓ No presence in insights report")
    print("  ✓ Ready for new merchant outreach")
    print("\nScript completed successfully!")


if __name__ == "__main__":
    main()
