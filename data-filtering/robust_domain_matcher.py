#!/usr/bin/env python3
"""
Robust Domain Matching System for Zero Data Loss

This script implements a comprehensive domain matching strategy that:
1. Creates multiple normalized versions of each domain
2. Uses fuzzy matching for edge cases
3. Provides detailed matching reports
4. Ensures zero data loss through comprehensive logging

Key Features:
- Multiple normalization strategies
- Subdomain handling (e.g., myshopify.com domains)
- URL parameter stripping
- Fuzzy matching for typos/variations
- Comprehensive matching reports
"""

import pandas as pd
import re
from urllib.parse import urlparse, unquote
from difflib import SequenceMatcher
import tldextract
from collections import defaultdict


class RobustDomainMatcher:
    def __init__(self):
        self.match_log = []
        self.unmatched_domains = []
        self.fuzzy_matches = []
        
    def extract_all_domain_variants(self, domain_input):
        """
        Extract all possible domain variants for comprehensive matching
        
        Args:
            domain_input (str): Raw domain/URL input
            
        Returns:
            set: All possible normalized domain variants
        """
        if not domain_input or pd.isna(domain_input):
            return set()
        
        variants = set()
        domain_input = str(domain_input).strip()
        
        # Strategy 1: Direct domain normalization
        normalized = self.normalize_domain_basic(domain_input)
        if normalized:
            variants.add(normalized)
        
        # Strategy 2: URL parsing
        url_domain = self.extract_domain_from_url(domain_input)
        if url_domain:
            variants.add(url_domain)
        
        # Strategy 3: TLD extraction (handles complex cases)
        tld_domain = self.extract_domain_with_tld(domain_input)
        if tld_domain:
            variants.add(tld_domain)
        
        # Strategy 4: Handle special cases (shopify, etc.)
        special_domain = self.handle_special_domains(domain_input)
        if special_domain:
            variants.add(special_domain)
        
        # Strategy 5: Clean variants (remove common prefixes/suffixes)
        for variant in list(variants):
            cleaned = self.clean_domain_variant(variant)
            if cleaned:
                variants.add(cleaned)
        
        return variants
    
    def normalize_domain_basic(self, domain):
        """Basic domain normalization"""
        if not domain or pd.isna(domain):
            return None
        
        domain = str(domain).lower().strip()
        
        # Remove common prefixes
        for prefix in ['http://', 'https://', 'www.', 'ftp://']:
            if domain.startswith(prefix):
                domain = domain[len(prefix):]
        
        # Remove port numbers
        if ':' in domain:
            domain = domain.split(':')[0]
        
        # Remove paths and parameters
        if '/' in domain:
            domain = domain.split('/')[0]
        
        # Remove query parameters
        if '?' in domain:
            domain = domain.split('?')[0]
        
        return domain if domain else None
    
    def extract_domain_from_url(self, url):
        """Extract domain using URL parsing"""
        if not url or pd.isna(url):
            return None
        
        try:
            # Ensure URL has protocol
            if not url.startswith(('http://', 'https://')):
                url = 'https://' + url
            
            parsed = urlparse(url)
            domain = parsed.netloc.lower()
            
            # Remove www prefix
            if domain.startswith('www.'):
                domain = domain[4:]
            
            # Remove port
            if ':' in domain:
                domain = domain.split(':')[0]
            
            return domain if domain else None
        except:
            return None
    
    def extract_domain_with_tld(self, domain_input):
        """Extract domain using TLD extraction (handles complex TLDs)"""
        try:
            # Clean the input first
            cleaned = self.normalize_domain_basic(domain_input)
            if not cleaned:
                return None
            
            extracted = tldextract.extract(cleaned)
            if extracted.domain and extracted.suffix:
                return f"{extracted.domain}.{extracted.suffix}"
            return None
        except:
            return None
    
    def handle_special_domains(self, domain_input):
        """Handle special domain cases like Shopify"""
        if not domain_input:
            return None
        
        domain = str(domain_input).lower()
        
        # Handle Shopify domains - keep the full subdomain
        if 'myshopify.com' in domain:
            # Extract the shopify subdomain
            match = re.search(r'([a-zA-Z0-9\-]+)\.myshopify\.com', domain)
            if match:
                return f"{match.group(1)}.myshopify.com"
        
        # Handle other special cases as needed
        return None
    
    def clean_domain_variant(self, domain):
        """Clean domain variants for better matching"""
        if not domain:
            return None
        
        # Remove trailing dots
        domain = domain.rstrip('.')
        
        # Handle encoded URLs
        try:
            domain = unquote(domain)
        except:
            pass
        
        return domain if domain else None
    
    def fuzzy_match_domains(self, target_domain, candidate_domains, threshold=0.85):
        """
        Perform fuzzy matching for domains that don't match exactly
        
        Args:
            target_domain (str): Domain to match
            candidate_domains (set): Set of candidate domains
            threshold (float): Similarity threshold (0-1)
            
        Returns:
            tuple: (best_match, similarity_score) or (None, 0)
        """
        best_match = None
        best_score = 0
        
        for candidate in candidate_domains:
            similarity = SequenceMatcher(None, target_domain, candidate).ratio()
            if similarity > best_score and similarity >= threshold:
                best_score = similarity
                best_match = candidate
        
        return best_match, best_score
    
    def create_domain_lookup(self, domains_list, source_name):
        """
        Create a comprehensive domain lookup dictionary
        
        Args:
            domains_list (list): List of domains
            source_name (str): Name of the source (for logging)
            
        Returns:
            dict: Mapping of all variants to original domain
        """
        lookup = {}
        variant_count = defaultdict(int)
        
        print(f"Creating domain lookup for {source_name}...")
        
        for original_domain in domains_list:
            if not original_domain:
                continue
                
            variants = self.extract_all_domain_variants(original_domain)
            
            for variant in variants:
                if variant:
                    lookup[variant] = original_domain
                    variant_count[variant] += 1
        
        # Log duplicate variants (potential conflicts)
        duplicates = {k: v for k, v in variant_count.items() if v > 1}
        if duplicates:
            print(f"Warning: Found {len(duplicates)} duplicate variants in {source_name}")
            for variant, count in list(duplicates.items())[:5]:  # Show first 5
                print(f"  '{variant}' appears {count} times")
        
        print(f"Created {len(lookup)} domain variants from {len(domains_list)} original domains")
        return lookup
    
    def match_domain_comprehensive(self, target_domain, shopmy_lookup, insights_lookup):
        """
        Comprehensive domain matching with fallback strategies
        
        Args:
            target_domain (str): Domain to match
            shopmy_lookup (dict): ShopMy domain lookup
            insights_lookup (dict): Insights domain lookup
            
        Returns:
            tuple: (shopmy_match, insights_match, match_details)
        """
        match_details = {
            'target': target_domain,
            'shopmy_match': False,
            'insights_match': False,
            'shopmy_method': None,
            'insights_method': None,
            'shopmy_original': None,
            'insights_original': None
        }
        
        if not target_domain:
            return False, False, match_details
        
        # Get all variants of the target domain
        target_variants = self.extract_all_domain_variants(target_domain)
        
        # Strategy 1: Exact variant matching
        shopmy_match = False
        insights_match = False
        
        for variant in target_variants:
            if variant in shopmy_lookup:
                shopmy_match = True
                match_details['shopmy_match'] = True
                match_details['shopmy_method'] = 'exact_variant'
                match_details['shopmy_original'] = shopmy_lookup[variant]
                break
        
        for variant in target_variants:
            if variant in insights_lookup:
                insights_match = True
                match_details['insights_match'] = True
                match_details['insights_method'] = 'exact_variant'
                match_details['insights_original'] = insights_lookup[variant]
                break
        
        # Strategy 2: Fuzzy matching (if no exact match)
        if not shopmy_match:
            best_match, score = self.fuzzy_match_domains(target_domain, set(shopmy_lookup.keys()))
            if best_match:
                shopmy_match = True
                match_details['shopmy_match'] = True
                match_details['shopmy_method'] = f'fuzzy_{score:.2f}'
                match_details['shopmy_original'] = shopmy_lookup[best_match]
                self.fuzzy_matches.append({
                    'target': target_domain,
                    'matched': best_match,
                    'score': score,
                    'source': 'shopmy'
                })
        
        if not insights_match:
            best_match, score = self.fuzzy_match_domains(target_domain, set(insights_lookup.keys()))
            if best_match:
                insights_match = True
                match_details['insights_match'] = True
                match_details['insights_method'] = f'fuzzy_{score:.2f}'
                match_details['insights_original'] = insights_lookup[best_match]
                self.fuzzy_matches.append({
                    'target': target_domain,
                    'matched': best_match,
                    'score': score,
                    'source': 'insights'
                })
        
        # Log the match
        self.match_log.append(match_details)
        
        # Log unmatched domains
        if not shopmy_match and not insights_match:
            self.unmatched_domains.append(target_domain)
        
        return shopmy_match, insights_match, match_details
    
    def generate_matching_report(self, output_file="domain_matching_report.txt"):
        """Generate a comprehensive matching report"""
        with open(output_file, 'w') as f:
            f.write("DOMAIN MATCHING REPORT\n")
            f.write("=" * 50 + "\n\n")
            
            # Summary statistics
            total_matches = len(self.match_log)
            shopmy_matches = sum(1 for m in self.match_log if m['shopmy_match'])
            insights_matches = sum(1 for m in self.match_log if m['insights_match'])
            both_matches = sum(1 for m in self.match_log if m['shopmy_match'] and m['insights_match'])
            unmatched = len(self.unmatched_domains)
            
            f.write(f"SUMMARY STATISTICS\n")
            f.write(f"Total domains processed: {total_matches}\n")
            f.write(f"ShopMy matches: {shopmy_matches} ({shopmy_matches/total_matches*100:.1f}%)\n")
            f.write(f"Insights matches: {insights_matches} ({insights_matches/total_matches*100:.1f}%)\n")
            f.write(f"Both sources: {both_matches} ({both_matches/total_matches*100:.1f}%)\n")
            f.write(f"Unmatched: {unmatched} ({unmatched/total_matches*100:.1f}%)\n\n")
            
            # Fuzzy matches
            if self.fuzzy_matches:
                f.write(f"FUZZY MATCHES ({len(self.fuzzy_matches)})\n")
                f.write("-" * 30 + "\n")
                for match in self.fuzzy_matches[:20]:  # Show first 20
                    f.write(f"{match['target']} -> {match['matched']} (score: {match['score']:.2f}, source: {match['source']})\n")
                if len(self.fuzzy_matches) > 20:
                    f.write(f"... and {len(self.fuzzy_matches) - 20} more\n")
                f.write("\n")
            
            # Unmatched domains
            if self.unmatched_domains:
                f.write(f"UNMATCHED DOMAINS ({len(self.unmatched_domains)})\n")
                f.write("-" * 30 + "\n")
                for domain in self.unmatched_domains[:50]:  # Show first 50
                    f.write(f"{domain}\n")
                if len(self.unmatched_domains) > 50:
                    f.write(f"... and {len(self.unmatched_domains) - 50} more\n")
        
        print(f"Matching report saved to: {output_file}")


# Install required package if not available
try:
    import tldextract
except ImportError:
    print("Installing tldextract package...")
    import subprocess
    subprocess.check_call(["pip", "install", "tldextract"])
    import tldextract
