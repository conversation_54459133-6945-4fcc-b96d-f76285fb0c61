#!/usr/bin/env python3
"""
Test script for the filtering functionality with a small subset of data.

This script creates a small test dataset and validates the filtering logic
before running on the full 45k dataset.
"""

import pandas as pd
import tempfile
import os
from pathlib import Path

# Import the filtering functions
from add_filtering_columns import (
    normalize_domain, 
    load_shopmy_domains, 
    load_insights_domains, 
    add_filtering_columns
)


def create_test_data():
    """Create test datasets for validation."""
    
    # Create test classified data
    test_classified = [
        {
            'name': 'Nike',
            'domain': 'nike.com',
            'fullPayout': 5.0,
            'rateType': 'percentage',
            'On ShopMy?': 'Yes',
            'Cookie Window': 30.0,
            'niche_classification': 'Sports & Fitness'
        },
        {
            'name': 'Sephora',
            'domain': 'sephora.com',
            'fullPayout': 8.0,
            'rateType': 'percentage',
            'On ShopMy?': 'Yes',
            'Cookie Window': 7.0,
            'niche_classification': 'Beauty & Skincare'
        },
        {
            'name': 'Unknown Brand',
            'domain': 'unknownbrand.com',
            'fullPayout': 3.0,
            'rateType': 'percentage',
            'On ShopMy?': 'No',
            'Cookie Window': 24.0,
            'niche_classification': 'Other'
        },
        {
            'name': 'Test Store',
            'domain': 'teststore.com',
            'fullPayout': 10.0,
            'rateType': 'percentage',
            'On ShopMy?': 'No',
            'Cookie Window': 30.0,
            'niche_classification': 'Fashion & Apparel'
        }
    ]
    
    # Create test ShopMy data (with clicks)
    test_shopmy = [
        {
            'Brand': 'Nike',
            'Clicks': 150,
            'URL': 'https://nike.com'
        },
        {
            'Brand': 'Adidas',
            'Clicks': 75,
            'URL': 'https://adidas.com'
        },
        {
            'Brand': 'Unknown Brand',
            'Clicks': 0,
            'URL': 'https://unknownbrand.com'
        },
        {
            'Brand': 'Test Store',
            'Clicks': 25,
            'URL': 'https://teststore.com'
        }
    ]
    
    # Create test insights data
    test_insights = [
        {'hostname': 'sephora.com'},
        {'hostname': 'ulta.com'},
        {'hostname': 'teststore.com'},
        {'hostname': 'example.com'}
    ]
    
    return test_classified, test_shopmy, test_insights


def test_domain_normalization():
    """Test the domain normalization function."""
    print("🧪 Testing domain normalization...")
    
    test_cases = [
        ("nike.com", "nike.com"),
        ("www.nike.com", "nike.com"),
        ("https://nike.com", "nike.com"),
        ("https://www.nike.com", "nike.com"),
        ("nike.com:8080", "nike.com"),
        ("", None),
        (None, None),
    ]
    
    for input_domain, expected in test_cases:
        result = normalize_domain(input_domain)
        status = "✅" if result == expected else "❌"
        print(f"  {status} '{input_domain}' -> '{result}' (expected: '{expected}')")
    
    print("Domain normalization test completed.\n")


def test_filtering_logic():
    """Test the complete filtering logic with test data."""
    print("🧪 Testing filtering logic with test data...")
    
    # Create test data
    test_classified, test_shopmy, test_insights = create_test_data()
    
    # Create temporary files
    with tempfile.TemporaryDirectory() as temp_dir:
        # Save test data to temporary files
        classified_file = os.path.join(temp_dir, "test_classified.csv")
        shopmy_file = os.path.join(temp_dir, "test_shopmy.csv")
        insights_file = os.path.join(temp_dir, "test_insights.csv")
        output_file = os.path.join(temp_dir, "test_output.csv")
        
        pd.DataFrame(test_classified).to_csv(classified_file, index=False)
        pd.DataFrame(test_shopmy).to_csv(shopmy_file, index=False)
        pd.DataFrame(test_insights).to_csv(insights_file, index=False)
        
        try:
            # Load domains from test files
            print("  Loading ShopMy domains...")
            shopmy_domains = load_shopmy_domains(shopmy_file)
            print(f"    Found domains: {sorted(shopmy_domains)}")
            
            print("  Loading insights domains...")
            insights_domains = load_insights_domains(insights_file)
            print(f"    Found domains: {sorted(insights_domains)}")
            
            # Add filtering columns
            print("  Adding filtering columns...")
            total_rows, shopmy_matches, insights_matches = add_filtering_columns(
                classified_file, shopmy_domains, insights_domains, output_file
            )
            
            # Verify results
            result_df = pd.read_csv(output_file)
            print(f"  Total rows processed: {total_rows}")
            print(f"  ShopMy matches: {shopmy_matches}")
            print(f"  Insights matches: {insights_matches}")
            
            # Check specific expected results
            print("\n  Detailed results:")
            for idx, row in result_df.iterrows():
                shopmy_status = "✓" if row["present_in_shopmy_links"] else "✗"
                insights_status = "✓" if row["present_in_mixpanel_insights"] else "✗"
                print(f"    {row['name']}: ShopMy {shopmy_status}, Insights {insights_status}")
            
            # Validate expected results
            expected_results = {
                'Nike': {'shopmy': True, 'insights': False},  # Has clicks in ShopMy
                'Sephora': {'shopmy': False, 'insights': True},  # In insights but no clicks
                'Unknown Brand': {'shopmy': False, 'insights': False},  # 0 clicks, not in insights
                'Test Store': {'shopmy': True, 'insights': True}  # Has clicks and in insights
            }
            
            print("\n  Validation:")
            all_correct = True
            for idx, row in result_df.iterrows():
                name = row['name']
                if name in expected_results:
                    expected = expected_results[name]
                    actual_shopmy = row['present_in_shopmy_links']
                    actual_insights = row['present_in_mixpanel_insights']
                    
                    shopmy_correct = actual_shopmy == expected['shopmy']
                    insights_correct = actual_insights == expected['insights']
                    
                    if shopmy_correct and insights_correct:
                        print(f"    ✅ {name}: Correct")
                    else:
                        print(f"    ❌ {name}: Expected ShopMy={expected['shopmy']}, Insights={expected['insights']}")
                        print(f"       Got ShopMy={actual_shopmy}, Insights={actual_insights}")
                        all_correct = False
            
            return all_correct
            
        except Exception as e:
            print(f"  ❌ Error during testing: {e}")
            return False


def main():
    """Run all tests."""
    print("🚀 Starting Filtering Logic Tests")
    print("=" * 60)
    
    # Test 1: Domain normalization
    test_domain_normalization()
    
    # Test 2: Complete filtering logic
    success = test_filtering_logic()
    
    print("=" * 60)
    if success:
        print("✅ All tests passed! The filtering logic is working correctly.")
        print("You can now run the full script with: python add_filtering_columns.py")
    else:
        print("❌ Some tests failed. Please check the logic before running on full dataset.")
    
    return success


if __name__ == "__main__":
    main()
