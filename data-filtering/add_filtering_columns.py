#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to add filtering columns to the classified ShopMy dataset.

This script:
1. Loads the classified dataset (classified_shopmy_niches_20250711_125052.csv)
2. Checks each domain against ShopMy links data (shopmy.csv)
3. Checks each domain against Mixpanel insights data (Untitled Report_Insights_2025-06-10_to_2025-07-10.csv)
4. Adds two new columns: 'present_in_shopmy_links' and 'present_in_mixpanel_insights'
5. Outputs a new CSV with all original data plus the two filtering columns

Usage:
    python add_filtering_columns.py
"""

import pandas as pd
import sys
from pathlib import Path
from urllib.parse import urlparse
import re
from datetime import datetime


def normalize_domain(domain):
    """
    Normalize domain for consistent matching

    Args:
        domain (str): Domain to normalize

    Returns:
        str: Normalized domain or None if invalid
    """
    if not domain or pd.isna(domain):
        return None

    domain = str(domain).lower().strip()

    # Remove protocol if present
    if domain.startswith(("http://", "https://")):
        try:
            parsed = urlparse(domain)
            domain = parsed.netloc.lower()
        except:
            pass

    # Remove www. prefix for normalization
    if domain.startswith("www."):
        domain = domain[4:]

    # Remove port numbers if present
    if ":" in domain:
        domain = domain.split(":")[0]

    return domain if domain else None


def extract_domain_from_url(url):
    """
    Extract and normalize domain from URL

    Args:
        url (str): URL to extract domain from

    Returns:
        str: Normalized domain name or None if invalid
    """
    if not url or pd.isna(url):
        return None

    try:
        # Handle URLs that might not have protocol
        if not url.startswith(("http://", "https://")):
            url = "https://" + url

        parsed = urlparse(url)
        domain = parsed.netloc.lower()

        # Remove www. prefix for normalization
        if domain.startswith("www."):
            domain = domain[4:]

        # Remove port numbers if present
        if ":" in domain:
            domain = domain.split(":")[0]

        return domain if domain else None

    except Exception as e:
        print(f"Error parsing URL '{url}': {e}")
        return None


def load_shopmy_domains(shopmy_file):
    """
    Load domains from shopmy.csv that have clicks > 0

    Args:
        shopmy_file (str): Path to shopmy.csv file

    Returns:
        set: Set of domains that have clicks in ShopMy
    """
    domains_with_clicks = set()

    try:
        df = pd.read_csv(shopmy_file)
        print(f"Loaded {len(df)} rows from {shopmy_file}")

        # Filter for rows with clicks > 0
        df["Clicks"] = pd.to_numeric(df["Clicks"], errors="coerce")
        df_with_clicks = df[df["Clicks"] > 0]

        print(f"Found {len(df_with_clicks)} rows with clicks > 0")

        # Extract domains from URL column (last column)
        url_column = df_with_clicks.columns[-1]  # Last column should be URL
        print(f"Extracting domains from column: {url_column}")

        for idx, url in enumerate(df_with_clicks[url_column]):
            domain = extract_domain_from_url(url)
            if domain:
                domains_with_clicks.add(domain)

            # Progress indicator for large files
            if (idx + 1) % 1000 == 0:
                print(f"Processed {idx + 1} URLs...")

        print(
            f"Extracted {len(domains_with_clicks)} unique domains with clicks from ShopMy"
        )

    except Exception as e:
        print(f"Error reading {shopmy_file}: {e}")
        return set()

    return domains_with_clicks


def load_insights_domains(insights_file):
    """
    Load domains from the insights report CSV

    Args:
        insights_file (str): Path to insights CSV file

    Returns:
        set: Set of normalized domains from insights report
    """
    try:
        df = pd.read_csv(insights_file)
        print(f"Loaded {len(df)} rows from insights report")

        # Extract domains from hostname column
        if "hostname" not in df.columns:
            print("Error: 'hostname' column not found in insights report")
            return set()

        domains = set()
        for idx, hostname in enumerate(df["hostname"].dropna()):
            normalized = normalize_domain(hostname)
            if normalized:
                domains.add(normalized)

            # Progress indicator for large files
            if (idx + 1) % 1000 == 0:
                print(f"Processed {idx + 1} hostnames...")

        print(f"Extracted {len(domains)} unique domains from insights report")
        return domains

    except Exception as e:
        print(f"Error reading insights file {insights_file}: {e}")
        return set()


def add_filtering_columns(
    classified_file, shopmy_domains, insights_domains, output_file
):
    """
    Add filtering columns to the classified dataset

    Args:
        classified_file (str): Path to classified CSV file
        shopmy_domains (set): Set of domains from ShopMy with clicks
        insights_domains (set): Set of domains from insights report
        output_file (str): Path to output CSV file

    Returns:
        tuple: (total_rows, shopmy_matches, insights_matches)
    """
    try:
        df = pd.read_csv(classified_file)
        print(f"\nProcessing classified dataset: {classified_file}")
        print(f"Loaded {len(df)} rows")

        # Initialize the new columns
        df["present_in_shopmy_links"] = False
        df["present_in_mixpanel_insights"] = False

        # Check each domain against both datasets
        shopmy_matches = 0
        insights_matches = 0

        for idx, row in df.iterrows():
            domain = normalize_domain(row["domain"])

            if domain:
                # Check against ShopMy domains
                if domain in shopmy_domains:
                    df.at[idx, "present_in_shopmy_links"] = True
                    shopmy_matches += 1

                # Check against insights domains
                if domain in insights_domains:
                    df.at[idx, "present_in_mixpanel_insights"] = True
                    insights_matches += 1

            # Progress indicator
            if (idx + 1) % 1000 == 0:
                print(f"Processed {idx + 1} domains...")

        # Save the results
        df.to_csv(output_file, index=False)

        total_rows = len(df)
        print(f"\nResults:")
        print(f"Total domains processed: {total_rows}")
        print(f"Domains present in ShopMy links: {shopmy_matches}")
        print(f"Domains present in Mixpanel insights: {insights_matches}")
        print(f"Saved results to: {output_file}")

        return total_rows, shopmy_matches, insights_matches

    except Exception as e:
        print(f"Error processing {classified_file}: {e}")
        return 0, 0, 0


def print_sample_results(output_file, sample_size=10):
    """Print a sample of the results for verification"""
    try:
        df = pd.read_csv(output_file)

        print(f"\nSample results (first {sample_size} rows):")
        print("-" * 100)

        # Show relevant columns
        columns_to_show = [
            "name",
            "domain",
            "niche_classification",
            "present_in_shopmy_links",
            "present_in_mixpanel_insights",
        ]
        sample_df = df[columns_to_show].head(sample_size)

        for idx, row in sample_df.iterrows():
            shopmy_status = "✓" if row["present_in_shopmy_links"] else "✗"
            insights_status = "✓" if row["present_in_mixpanel_insights"] else "✗"
            print(
                f"{row['name'][:30]:<30} | {row['domain'][:25]:<25} | {row['niche_classification'][:20]:<20} | ShopMy: {shopmy_status} | Insights: {insights_status}"
            )

        print("-" * 100)

        # Show summary statistics
        shopmy_count = df["present_in_shopmy_links"].sum()
        insights_count = df["present_in_mixpanel_insights"].sum()
        both_count = (
            (df["present_in_shopmy_links"]) & (df["present_in_mixpanel_insights"])
        ).sum()
        neither_count = (
            (~df["present_in_shopmy_links"]) & (~df["present_in_mixpanel_insights"])
        ).sum()

        print(f"\nSummary Statistics:")
        print(
            f"Present in ShopMy links: {shopmy_count} ({shopmy_count/len(df)*100:.1f}%)"
        )
        print(
            f"Present in Mixpanel insights: {insights_count} ({insights_count/len(df)*100:.1f}%)"
        )
        print(f"Present in both: {both_count} ({both_count/len(df)*100:.1f}%)")
        print(f"Present in neither: {neither_count} ({neither_count/len(df)*100:.1f}%)")

    except Exception as e:
        print(f"Error showing sample results: {e}")


def main():
    """Main function to orchestrate the filtering process"""

    # File paths
    classified_file = "classified_shopmy_niches_20250711_125052.csv"
    shopmy_file = "shopmy.csv"
    insights_file = "Untitled Report_Insights_2025-06-10_to_2025-07-10.csv"

    # Output file path
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = f"classified_shopmy_with_filters_{timestamp}.csv"

    print("=" * 80)
    print("ADDING FILTERING COLUMNS TO CLASSIFIED DATASET")
    print("=" * 80)

    # Check if input files exist
    for file_path in [classified_file, shopmy_file, insights_file]:
        if not Path(file_path).exists():
            print(f"Error: File '{file_path}' not found!")
            sys.exit(1)

    print("All input files found. Starting processing...")

    # Step 1: Load domains from ShopMy data
    print("\nStep 1: Loading domains from ShopMy data")
    shopmy_domains = load_shopmy_domains(shopmy_file)

    # Step 2: Load domains from insights report
    print("\nStep 2: Loading domains from insights report")
    insights_domains = load_insights_domains(insights_file)

    if not shopmy_domains and not insights_domains:
        print("No domains found in either dataset. Exiting.")
        sys.exit(1)

    # Step 3: Add filtering columns to classified dataset
    print("\nStep 3: Adding filtering columns to classified dataset")
    total_rows, shopmy_matches, insights_matches = add_filtering_columns(
        classified_file, shopmy_domains, insights_domains, output_file
    )

    # Step 4: Show sample results
    print_sample_results(output_file)

    # Final summary
    print("\n" + "=" * 80)
    print("FINAL SUMMARY")
    print("=" * 80)
    print(f"Input file: {classified_file}")
    print(f"Output file: {output_file}")
    print(f"Total domains processed: {total_rows}")
    print(f"ShopMy domains loaded: {len(shopmy_domains)}")
    print(f"Insights domains loaded: {len(insights_domains)}")
    print(f"Matches in ShopMy: {shopmy_matches}")
    print(f"Matches in Insights: {insights_matches}")

    print(f"\nNew columns added:")
    print(f"  ✓ present_in_shopmy_links (True/False)")
    print(f"  ✓ present_in_mixpanel_insights (True/False)")

    print(f"\nScript completed successfully!")


if __name__ == "__main__":
    main()
