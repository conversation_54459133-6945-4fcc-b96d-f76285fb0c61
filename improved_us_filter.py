import csv
import re
from datetime import datetime

def improved_us_filter():
    """
    Improved US merchant filtering with better detection logic
    """
    print("Running improved US merchant filtering...")
    
    input_file = "/Users/<USER>/Documents/GitHub/phia/data-pipeline/affiliate_merchants_20250717_162247.csv"
    
    # Create timestamp for output filename
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = f"affiliate_merchants_US_improved_{timestamp}.csv"
    
    us_merchants = []
    total_merchants = 0
    
    # Comprehensive US detection criteria
    def is_us_merchant(merchant):
        source = merchant.get('source', '').strip()
        network = merchant.get('network', '').strip()
        name = merchant.get('name', '').strip()
        
        # Combine all text for analysis
        combined_text = f"{source} {network} {name}".upper()
        
        # 1. Explicit US indicators in network/source
        us_network_indicators = [
            'US', 'USA', 'UNITED STATES', 'AMERICA', 'AMERICAN'
        ]
        
        for indicator in us_network_indicators:
            if indicator in source.upper() or indicator in network.upper():
                return True
        
        # 2. Known US-only affiliate networks (even without "US" in name)
        us_only_networks = [
            'ShareASale', 'ShareASale Coupons/Deals',
            'GoAffPro US', 'Partnerize US', 'Tradedoubler US'
        ]
        
        if network in us_only_networks:
            return True
        
        # 3. US company patterns in merchant name
        us_company_patterns = [
            r'\bUSA\b', r'\bAMERIC[A|AN]\b', r'\bUNITED STATES\b'
        ]
        
        for pattern in us_company_patterns:
            if re.search(pattern, name.upper()):
                return True
        
        # 4. Known major US retailers/brands
        us_major_brands = [
            'WALMART', 'TARGET', 'AMAZON', 'NORDSTROM', 'MACY', 'MACYS',
            'BEST BUY', 'HOME DEPOT', 'COSTCO', 'SEARS', 'KOHLS',
            'JC PENNEY', 'JCPENNEY', 'BARNES & NOBLE', 'STAPLES',
            'OFFICE DEPOT', 'PETCO', 'PETSMART', 'GAMESTOP',
            'AMERICAN EAGLE', 'ABERCROMBIE', 'HOLLISTER',
            'VICTORIAS SECRET', 'BATH & BODY WORKS'
        ]
        
        name_upper = name.upper()
        for brand in us_major_brands:
            if brand in name_upper:
                return True
        
        # 5. .com domains that are likely US (with some exclusions)
        if '.com' in name.lower():
            # Exclude if clearly from other countries
            non_us_indicators = [
                'UK', 'GERMANY', 'FRANCE', 'CANADA', 'AUSTRALIA', 
                'NETHERLANDS', 'BELGIUM', 'DENMARK', 'SWEDEN', 
                'NORWAY', 'FINLAND', 'SPAIN', 'ITALY', 'SWITZERLAND'
            ]
            
            # Check if network suggests non-US
            network_upper = network.upper()
            is_non_us_network = any(indicator in network_upper for indicator in non_us_indicators)
            
            # If it's a .com but not clearly from another country, consider it US
            if not is_non_us_network:
                return True
        
        return False
    
    # Read and filter the CSV
    print("Reading and analyzing merchants with improved logic...")
    with open(input_file, 'r', encoding='utf-8') as infile:
        reader = csv.DictReader(infile)
        
        for row in reader:
            total_merchants += 1
            
            if is_us_merchant(row):
                us_merchants.append(row)
    
    # Write filtered results to new CSV
    print(f"Writing {len(us_merchants)} US merchants to {output_file}...")
    with open(output_file, 'w', newline='', encoding='utf-8') as outfile:
        if us_merchants:
            fieldnames = us_merchants[0].keys()
            writer = csv.DictWriter(outfile, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(us_merchants)
    
    print(f"Successfully created {output_file}")
    
    # Print summary statistics
    print(f"\nImproved Filtering Summary:")
    print(f"Total merchants processed: {total_merchants:,}")
    print(f"US merchants found: {len(us_merchants):,}")
    print(f"Percentage US merchants: {(len(us_merchants)/total_merchants)*100:.1f}%")
    
    # Compare with previous filtering
    previous_count = 3295  # From previous filtering
    improvement = len(us_merchants) - previous_count
    print(f"Previous filtering found: {previous_count:,}")
    print(f"Improvement: +{improvement:,} additional US merchants")
    
    # Show sample of newly found merchants
    print(f"\nSample US merchants:")
    for i, merchant in enumerate(us_merchants[:10]):
        print(f"  {i+1:2d}. {merchant.get('name', 'N/A'):<35} - {merchant.get('network', 'N/A'):<30}")
    
    # Show network distribution for US merchants
    print(f"\nUS Network distribution:")
    networks = {}
    for merchant in us_merchants:
        network = merchant.get('network', 'Unknown')
        if network not in networks:
            networks[network] = 0
        networks[network] += 1
    
    # Sort networks by count and show top 15
    sorted_networks = sorted(networks.items(), key=lambda x: x[1], reverse=True)
    for network, count in sorted_networks[:15]:
        print(f"  {network:<40} - {count:4d} merchants")
    
    return output_file

if __name__ == "__main__":
    output_file = improved_us_filter()
    print(f"\n✅ Improved US merchants CSV created: {output_file}")
