import csv
import re
from datetime import datetime

def filter_us_merchants():
    """
    Filter the affiliate merchants CSV to include only US merchants
    """
    print("Filtering affiliate merchants for US-only merchants...")
    
    input_file = "/Users/<USER>/Documents/GitHub/phia/data-pipeline/affiliate_merchants_20250717_162247.csv"
    
    # Create timestamp for output filename
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = f"affiliate_merchants_US_only_{timestamp}.csv"
    
    us_merchants = []
    total_merchants = 0
    
    # US indicators to look for in source and network fields
    us_indicators = [
        'US',
        'United States', 
        'USA',
        'America',
        'American'
    ]
    
    # Read and filter the CSV
    print("Reading and analyzing merchants...")
    with open(input_file, 'r', encoding='utf-8') as infile:
        reader = csv.DictReader(infile)
        
        for row in reader:
            total_merchants += 1
            
            # Check if merchant is US-based by looking at source and network fields
            source = row.get('source', '').strip()
            network = row.get('network', '').strip()
            
            # Check if any US indicator is present in source or network
            is_us_merchant = False
            for indicator in us_indicators:
                if (indicator in source or indicator in network):
                    is_us_merchant = True
                    break
            
            if is_us_merchant:
                us_merchants.append(row)
    
    # Write filtered results to new CSV
    print(f"Writing {len(us_merchants)} US merchants to {output_file}...")
    with open(output_file, 'w', newline='', encoding='utf-8') as outfile:
        if us_merchants:
            fieldnames = us_merchants[0].keys()
            writer = csv.DictWriter(outfile, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(us_merchants)
    
    print(f"Successfully created {output_file}")
    
    # Print summary statistics
    print(f"\nFiltering Summary:")
    print(f"Total merchants processed: {total_merchants:,}")
    print(f"US merchants found: {len(us_merchants):,}")
    print(f"Percentage US merchants: {(len(us_merchants)/total_merchants)*100:.1f}%")
    
    # Show sample of US merchants
    print(f"\nSample US merchants:")
    for i, merchant in enumerate(us_merchants[:10]):
        print(f"  {i+1:2d}. {merchant.get('name', 'N/A'):<30} - {merchant.get('network', 'N/A'):<25} - {merchant.get('product_count', 'N/A'):>10} products")
    
    # Show network distribution for US merchants
    print(f"\nUS Network distribution:")
    networks = {}
    for merchant in us_merchants:
        network = merchant.get('network', 'Unknown')
        if network not in networks:
            networks[network] = 0
        networks[network] += 1
    
    # Sort networks by count and show top 10
    sorted_networks = sorted(networks.items(), key=lambda x: x[1], reverse=True)
    for network, count in sorted_networks[:10]:
        print(f"  {network:<35} - {count:4d} merchants")
    
    return output_file

if __name__ == "__main__":
    output_file = filter_us_merchants()
    print(f"\n✅ US merchants CSV created: {output_file}")
