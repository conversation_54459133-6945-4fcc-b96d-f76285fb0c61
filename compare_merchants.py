import csv
import re
from difflib import SequenceMatcher


def normalize_merchant_name(name):
    """
    Normalize merchant names for better matching
    """
    if not name:
        return ""

    # Convert to lowercase and remove extra spaces
    name = name.lower().strip()

    # Remove quotes
    name = name.replace('"', "").replace("'", "")

    # Remove common business suffixes
    name = re.sub(
        r"\s*(inc\.?|llc\.?|ltd\.?|corp\.?|corporation|company|co\.?)\s*$",
        "",
        name,
        flags=re.IGNORECASE,
    )

    # Remove "the" prefix
    name = re.sub(r"^\s*the\s+", "", name, flags=re.IGNORECASE)

    # Remove parenthetical content like "(US/CA)" or "(<PERSON><PERSON><PERSON> Marcus)"
    name = re.sub(r"\s*\([^)]*\)\s*", " ", name)

    # Remove special characters and normalize spaces
    name = re.sub(r"[^\w\s&]", " ", name)
    name = re.sub(r"\s+", " ", name).strip()

    # Remove common geographic indicators
    name = re.sub(r"\s+(us|usa|america|american)\s*$", "", name, flags=re.IGNORECASE)

    return name


def similarity_score(a, b):
    """
    Calculate similarity between two strings
    """
    return SequenceMatcher(None, a, b).ratio()


def find_missing_merchants():
    """
    Compare current merchants with US affiliate database to find missing opportunities
    """
    print("Comparing merchant databases...")

    current_file = "download (19).csv"
    affiliate_file = "/Users/<USER>/Documents/GitHub/phia/data-pipeline/affiliate_merchants_US_final_20250717_163734.csv"

    # Read current merchants
    print("Reading current merchants...")
    current_merchants = {}
    current_names = set()

    try:
        with open(current_file, "r", encoding="utf-8") as f:
            reader = csv.DictReader(f)

            for i, row in enumerate(reader):
                # Handle BOM character in column name
                name = row.get("advertiser_name", "") or row.get(
                    "\ufeffadvertiser_name", ""
                )
                name = name.strip()

                if name:
                    normalized = normalize_merchant_name(name)
                    current_merchants[normalized] = {
                        "original_name": name,
                        "network": row.get("network_name", ""),
                        "revenue": row.get("revenues", "0"),
                    }
                    current_names.add(normalized)
    except Exception as e:
        print(f"Error reading current merchants file: {e}")
        return

    print(f"Found {len(current_merchants)} current merchants")

    # Read affiliate merchants
    print("Reading US affiliate merchants...")
    affiliate_merchants = []

    with open(affiliate_file, "r", encoding="utf-8") as f:
        reader = csv.DictReader(f)
        for row in reader:
            name = row.get("name", "").strip()
            if name:
                normalized = normalize_merchant_name(name)
                affiliate_merchants.append(
                    {
                        "id": row.get("id", ""),
                        "name": name,
                        "normalized_name": normalized,
                        "network": row.get("network", ""),
                        "source": row.get("source", ""),
                        "product_count": row.get("product_count", "0"),
                        "logo": row.get("logo", ""),
                    }
                )

    print(f"Found {len(affiliate_merchants)} US affiliate merchants")

    # Find missing merchants
    print("Finding missing merchants...")
    missing_merchants = []
    matched_merchants = []
    similarity_threshold = 0.85

    for affiliate in affiliate_merchants:
        affiliate_name = affiliate["normalized_name"]

        # Check for exact match
        if affiliate_name in current_names:
            matched_merchants.append(
                {
                    "affiliate_name": affiliate["name"],
                    "current_name": current_merchants[affiliate_name]["original_name"],
                    "match_type": "exact",
                }
            )
            continue

        # Check for similar matches
        best_match = None
        best_score = 0

        for current_name in current_names:
            score = similarity_score(affiliate_name, current_name)
            if score > best_score:
                best_score = score
                best_match = current_name

        if best_score >= similarity_threshold:
            matched_merchants.append(
                {
                    "affiliate_name": affiliate["name"],
                    "current_name": current_merchants[best_match]["original_name"],
                    "match_type": f"similar ({best_score:.2f})",
                }
            )
        else:
            # This merchant is missing from our current list
            missing_merchants.append(affiliate)

    # Sort missing merchants by product count
    missing_merchants.sort(
        key=lambda x: int(x["product_count"].replace(",", "") or "0"), reverse=True
    )

    # Write missing merchants to CSV
    print(f"Writing {len(missing_merchants)} missing merchants to missing.csv...")

    with open("missing.csv", "w", newline="", encoding="utf-8") as f:
        fieldnames = [
            "merchant_name",
            "network",
            "product_count",
            "priority_tier",
            "logo_url",
            "merchant_id",
            "source",
        ]
        writer = csv.DictWriter(f, fieldnames=fieldnames)
        writer.writeheader()

        for merchant in missing_merchants:
            # Calculate priority tier based on product count
            product_count = int(merchant["product_count"].replace(",", "") or "0")

            if product_count >= 50000:
                tier = "Ultra High (50k+ products)"
            elif product_count >= 10000:
                tier = "High (10k-50k products)"
            elif product_count >= 1000:
                tier = "Medium-High (1k-10k products)"
            elif product_count >= 100:
                tier = "Medium (100-1k products)"
            elif product_count >= 10:
                tier = "Low-Medium (10-100 products)"
            else:
                tier = "Low (<10 products)"

            writer.writerow(
                {
                    "merchant_name": merchant["name"],
                    "network": merchant["network"],
                    "product_count": merchant["product_count"],
                    "priority_tier": tier,
                    "logo_url": merchant["logo"],
                    "merchant_id": merchant["id"],
                    "source": merchant["source"],
                }
            )

    # Print summary
    print(f"\n📊 COMPARISON SUMMARY:")
    print(f"Current merchants: {len(current_merchants):,}")
    print(f"US affiliate merchants: {len(affiliate_merchants):,}")
    print(f"Matched merchants: {len(matched_merchants):,}")
    print(f"Missing merchants: {len(missing_merchants):,}")
    print(f"Coverage: {(len(matched_merchants) / len(affiliate_merchants) * 100):.1f}%")

    # Show some matched merchants for verification
    print(f"\n✅ Sample matched merchants:")
    for i, match in enumerate(matched_merchants[:10]):
        print(
            f"  {i+1:2d}. {match['current_name']:<30} ↔ {match['affiliate_name']:<30} ({match['match_type']})"
        )

    # Show top missing merchants
    print(f"\n❌ Top 15 missing merchants by product count:")
    for i, merchant in enumerate(missing_merchants[:15]):
        print(
            f"  {i+1:2d}. {merchant['name']:<40} - {merchant['product_count']:>10} products ({merchant['network']})"
        )

    # Network distribution of missing merchants
    print(f"\n🌐 Missing merchants by network:")
    network_counts = {}
    for merchant in missing_merchants:
        network = merchant["network"]
        network_counts[network] = network_counts.get(network, 0) + 1

    sorted_networks = sorted(network_counts.items(), key=lambda x: x[1], reverse=True)
    for network, count in sorted_networks[:10]:
        print(f"  {network:<40} - {count:4d} merchants")

    # Priority distribution
    print(f"\n🎯 Priority distribution of missing merchants:")
    priority_counts = {}
    for merchant in missing_merchants:
        product_count = int(merchant["product_count"].replace(",", "") or "0")
        if product_count >= 50000:
            tier = "Ultra High (50k+ products)"
        elif product_count >= 10000:
            tier = "High (10k-50k products)"
        elif product_count >= 1000:
            tier = "Medium-High (1k-10k products)"
        elif product_count >= 100:
            tier = "Medium (100-1k products)"
        elif product_count >= 10:
            tier = "Low-Medium (10-100 products)"
        else:
            tier = "Low (<10 products)"

        priority_counts[tier] = priority_counts.get(tier, 0) + 1

    for tier, count in priority_counts.items():
        print(f"  {tier:<35} - {count:4d} merchants")


if __name__ == "__main__":
    find_missing_merchants()
    print(
        f"\n✅ Analysis complete! Check 'missing.csv' for the full list of missing merchants."
    )
