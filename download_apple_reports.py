#!/usr/bin/env python3
"""Download Apple Analytics reports for manual inspection"""

import os
import sys
import gzip
from datetime import datetime

# Add the dags/dependencies directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'dags', 'dependencies'))

from apple.apple_api_client import AppleAnalyticsClient
from apple.apple_config import REPORT_PROCESSING_CONFIG

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

def download_reports():
    """Download the 4 reports that the DAG fetches"""
    
    print("🍎 Initializing Apple Analytics client...")
    client = AppleAnalyticsClient()
    
    # The 4 reports we want to download
    target_reports = [
        "App Downloads Standard",
        "App Store Installation and Deletion Detailed", 
        "App Sessions Standard",
        "App Sessions Detailed"
    ]
    
    # Use the existing report request ID from config
    report_request_id = REPORT_PROCESSING_CONFIG["existing_report_request_id"]
    print(f"📋 Using report request ID: {report_request_id}")
    
    # Create output directory
    output_dir = "apple_reports_download"
    os.makedirs(output_dir, exist_ok=True)
    print(f"📁 Created output directory: {output_dir}")
    
    try:
        # Get all available reports
        print("\n📊 Fetching available reports...")
        all_reports = client._make_request("GET", f"/analyticsReportRequests/{report_request_id}/reports")
        available_reports = all_reports.json().get("data", [])
        
        # Find and download each target report
        for target_name in target_reports:
            print(f"\n{'='*60}")
            print(f"📥 Processing: {target_name}")
            
            # Find the report
            report = None
            for r in available_reports:
                if r.get("attributes", {}).get("name") == target_name:
                    report = r
                    break
            
            if not report:
                print(f"⚠️  Report not found: {target_name}")
                continue
            
            report_id = report["id"]
            print(f"✅ Found report ID: {report_id}")
            
            try:
                # Get the most recent instance (using our updated method)
                instance_id = client.get_instances(report_id)
                
                # Get segment URL
                segment_url = client.get_segments(instance_id)
                print(f"🔗 Got segment URL")
                
                # Download the data
                print(f"⬇️  Downloading report data...")
                raw_data = client.download_segment(segment_url)
                
                # Save the compressed file
                safe_filename = target_name.lower().replace(" ", "_").replace("/", "_")
                compressed_path = os.path.join(output_dir, f"{safe_filename}.gz")
                
                with open(compressed_path, 'wb') as f:
                    f.write(raw_data)
                print(f"💾 Saved compressed file: {compressed_path}")
                
                # Decompress and save as CSV
                try:
                    csv_content = gzip.decompress(raw_data).decode('utf-8')
                    csv_path = os.path.join(output_dir, f"{safe_filename}.csv")
                    
                    with open(csv_path, 'w') as f:
                        f.write(csv_content)
                    
                    print(f"📄 Saved CSV file: {csv_path}")
                    
                    # Show first few lines
                    lines = csv_content.split('\n')[:5]
                    print(f"\n📋 Preview (first 5 lines):")
                    for line in lines:
                        print(f"   {line[:100]}...")
                    
                except Exception as e:
                    print(f"⚠️  Could not decompress as CSV: {e}")
                
            except Exception as e:
                print(f"❌ Error downloading {target_name}: {e}")
                continue
        
        print(f"\n{'='*60}")
        print(f"✅ Download complete! Check the '{output_dir}' directory for files.")
        print(f"\nYou can open the CSV files in Excel or any spreadsheet application.")
        
    except Exception as e:
        print(f"\n❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    download_reports()