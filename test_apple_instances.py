#!/usr/bin/env python3
"""Test script to fetch all instances from Apple Analytics API"""

import os
import sys
import json
import requests
from datetime import datetime

# Add the dags/dependencies directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'dags', 'dependencies'))

from apple.jwt_service import AppleJWTService
from apple.apple_api_client import AppleAnalyticsClient

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

def test_get_instances():
    """Test fetching all instances for a report"""
    
    print("🔧 Initializing Apple Analytics client...")
    
    # Initialize the client
    client = AppleAnalyticsClient()
    
    # Get the report request ID from config
    report_request_id = "3bfe16a0-ded2-444c-82fb-ec819d301f01"
    
    print(f"📋 Using report request ID: {report_request_id}")
    
    try:
        # First, get all available reports
        print("\n📊 Fetching all available reports...")
        reports_response = client._make_request("GET", f"/analyticsReportRequests/{report_request_id}/reports")
        reports_data = reports_response.json()
        
        print(f"\n✅ Found {len(reports_data.get('data', []))} reports:")
        for report in reports_data.get('data', []):
            report_name = report.get('attributes', {}).get('name', 'Unknown')
            report_id = report.get('id')
            print(f"  - {report_name} (ID: {report_id})")
        
        # Now let's get instances for each report
        print("\n🔍 Fetching instances for each report...")
        
        # Find the reports we're actually interested in
        target_report_names = [
            "App Downloads Standard",
            "App Store Installation and Deletion Detailed", 
            "App Sessions Standard",
            "App Sessions Detailed"
        ]
        
        target_reports = []
        for report in reports_data.get('data', []):
            if report.get('attributes', {}).get('name') in target_report_names:
                target_reports.append(report)
        
        for report in target_reports:
            report_name = report.get('attributes', {}).get('name', 'Unknown')
            report_id = report.get('id')
            
            print(f"\n📦 Getting instances for: {report_name}")
            
            # Fetch ALL instances (not just the first one)
            instances_response = client._make_request("GET", f"/analyticsReports/{report_id}/instances")
            instances_data = instances_response.json()
            
            instances = instances_data.get('data', [])
            print(f"  Found {len(instances)} instances:")
            
            # Sort instances by processing date (newest first)
            sorted_instances = sorted(
                instances,
                key=lambda x: x.get('attributes', {}).get('processingDate', ''),
                reverse=True
            )
            
            # Show details for each instance
            for idx, instance in enumerate(sorted_instances[:5]):  # Show only first 5
                instance_id = instance.get('id')
                attrs = instance.get('attributes', {})
                processing_date = attrs.get('processingDate', 'Unknown')
                granularity = attrs.get('granularity', 'Unknown')
                
                print(f"\n  Instance {idx + 1}:")
                print(f"    ID: {instance_id}")
                print(f"    Processing Date: {processing_date}")
                print(f"    Granularity: {granularity}")
                
                # Convert processing date to datetime if possible
                try:
                    date_obj = datetime.fromisoformat(processing_date.replace('Z', '+00:00'))
                    days_ago = (datetime.now() - date_obj.replace(tzinfo=None)).days
                    print(f"    Days ago: {days_ago}")
                except:
                    pass
            
            if len(instances) > 5:
                print(f"\n  ... and {len(instances) - 5} more instances")
        
        # Test with curl command as well
        print("\n\n📡 Generating curl command for manual testing...")
        
        # Generate a fresh JWT token
        jwt_token = client.jwt_service.generate_jwt_token()
        
        # Pick the first report for the curl example
        if reports_data.get('data'):
            first_report_id = reports_data.get('data')[0].get('id')
            
            curl_command = f"""
curl -X GET \\
  'https://api.appstoreconnect.apple.com/v1/analyticsReports/{first_report_id}/instances' \\
  -H 'Authorization: Bearer {jwt_token}' \\
  -H 'Accept: application/json' | python -m json.tool
"""
            
            print("You can also test with this curl command:")
            print(curl_command)
        
    except Exception as e:
        print(f"\n❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_get_instances()