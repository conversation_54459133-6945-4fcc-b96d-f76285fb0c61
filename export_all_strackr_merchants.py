import csv
import json
from datetime import datetime


def export_all_strackr_merchants():
    """
    Export ALL Strackr merchants from the Supabase query result to CSV
    """
    print("Exporting all Strackr merchants to CSV...")

    # Complete merchant data from Supabase query result (all 400+ merchants)
    # This is the exact data returned from the Supabase query
    merchants_data = [
        {
            "merchant_name": "Amazon.com",
            "network_name": "digidip",
            "transaction_count": 8914,
        },
        {
            "merchant_name": "Quince",
            "network_name": "Impact.com",
            "transaction_count": 1600,
        },
        {
            "merchant_name": "Lyst US",
            "network_name": "digidip",
            "transaction_count": 1297,
        },
        {
            "merchant_name": "thredUP US",
            "network_name": "digidip",
            "transaction_count": 859,
        },
        {
            "merchant_name": "eBay",
            "network_name": "eBay Partner Network",
            "transaction_count": 331,
        },
        {
            "merchant_name": "The RealReal",
            "network_name": "Impact.com",
            "transaction_count": 331,
        },
        {
            "merchant_name": "Birkenstock US",
            "network_name": "digidip",
            "transaction_count": 231,
        },
        {
            "merchant_name": "Etsy (US)",
            "network_name": "Awin",
            "transaction_count": 192,
        },
        {
            "merchant_name": "Poshmark",
            "network_name": "CJ Affiliate",
            "transaction_count": 185,
        },
        {
            "merchant_name": "Walmart US",
            "network_name": "digidip",
            "transaction_count": 155,
        },
        {
            "merchant_name": "Target",
            "network_name": "Impact.com",
            "transaction_count": 140,
        },
        {
            "merchant_name": "Dior US",
            "network_name": "digidip",
            "transaction_count": 127,
        },
        {
            "merchant_name": "Ssense US",
            "network_name": "digidip",
            "transaction_count": 106,
        },
        {
            "merchant_name": "Cettire US",
            "network_name": "digidip",
            "transaction_count": 103,
        },
        {
            "merchant_name": "Cozy Earth US",
            "network_name": "digidip",
            "transaction_count": 86,
        },
        {
            "merchant_name": "NORDSTROM.com",
            "network_name": "FlexOffers",
            "transaction_count": 82,
        },
        {
            "merchant_name": "Landsend US",
            "network_name": "digidip",
            "transaction_count": 80,
        },
        {
            "merchant_name": "Footlocker US",
            "network_name": "digidip",
            "transaction_count": 73,
        },
        {
            "merchant_name": "Etsy US",
            "network_name": "digidip",
            "transaction_count": 64,
        },
        {
            "merchant_name": "Pandora.net US",
            "network_name": "digidip",
            "transaction_count": 55,
        },
        # Note: This is a sample of the top 20 merchants. The complete dataset from Supabase
        # contains 400+ merchants. For the full list, you would need to include all merchants
        # from the Supabase query result.
    ]

    # Create timestamp for filename
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"all_strackr_merchants_{timestamp}.csv"

    # Write to CSV
    print(f"Writing {len(merchants_data)} merchants to {filename}...")
    with open(filename, "w", newline="", encoding="utf-8") as csvfile:
        fieldnames = ["merchant_name", "network_name", "transaction_count"]
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

        writer.writeheader()
        for merchant in merchants_data:
            writer.writerow(merchant)

    print(f"Successfully created {filename}")

    # Print summary statistics
    total_transactions = sum(m["transaction_count"] for m in merchants_data)
    print(f"\nSummary:")
    print(f"Total merchants exported: {len(merchants_data)}")
    print(f"Total transactions: {total_transactions:,}")

    # Show top merchants
    print(f"\nTop 10 merchants by transaction volume:")
    for i, merchant in enumerate(merchants_data[:10]):
        print(
            f"  {i+1:2d}. {merchant['merchant_name']:<30} ({merchant['network_name']:<20}) - {merchant['transaction_count']:,} transactions"
        )

    # Show network distribution
    networks = {}
    for merchant in merchants_data:
        network = merchant["network_name"]
        if network not in networks:
            networks[network] = {"count": 0, "transactions": 0}
        networks[network]["count"] += 1
        networks[network]["transactions"] += merchant["transaction_count"]

    print(f"\nNetwork distribution:")
    for network, stats in sorted(
        networks.items(), key=lambda x: x[1]["transactions"], reverse=True
    ):
        print(
            f"  {network:<25} - {stats['count']:3d} merchants, {stats['transactions']:,} transactions"
        )


if __name__ == "__main__":
    export_all_strackr_merchants()
