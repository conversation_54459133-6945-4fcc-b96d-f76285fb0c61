import requests
import json
import csv
from datetime import datetime

def scrape_affiliate_merchants():
    """
    Simple script to scrape all merchant data from affiliate.com
    """
    print("Starting to scrape affiliate.com merchant data...")
    
    # The API endpoint we found
    url = "https://cdn.prod.website-files.com/64b56c11ff9f3a08b75196ad/68653f3c7d219a31a00273ee_data-1JUL25.txt"
    
    try:
        # Fetch the data
        print("Fetching data from API...")
        response = requests.get(url)
        response.raise_for_status()
        
        # Parse JSON
        data = response.json()
        merchants = data['data']
        
        print(f"Found {len(merchants)} merchants")
        
        # Prepare CSV file
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"affiliate_merchants_{timestamp}.csv"
        
        # Extract field names from first merchant
        if merchants:
            fieldnames = merchants[0].keys()
            
            # Write to CSV
            print(f"Writing data to {filename}...")
            with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()
                
                for merchant in merchants:
                    # Clean up the logo field (remove HTML tags)
                    if 'logo' in merchant:
                        merchant['logo'] = merchant['logo'].replace('<img src="', '').replace('"  onerror="this.style.display=\'none\'" />', '')
                    
                    writer.writerow(merchant)
            
            print(f"Successfully saved {len(merchants)} merchants to {filename}")
            
            # Print some sample data
            print("\nSample merchants:")
            for i, merchant in enumerate(merchants[:5]):
                print(f"{i+1}. {merchant.get('name', 'N/A')} - {merchant.get('network', 'N/A')} - {merchant.get('product_count', 'N/A')} products")
                
        else:
            print("No merchant data found")
            
    except requests.RequestException as e:
        print(f"Error fetching data: {e}")
    except json.JSONDecodeError as e:
        print(f"Error parsing JSON: {e}")
    except Exception as e:
        print(f"Unexpected error: {e}")

if __name__ == "__main__":
    scrape_affiliate_merchants()
