"""
SearchAPI Product URL Extractor for ShopMy Merchants
This script reads domains from a CSV file and attempts to find product URLs using SearchAPI.
Optimized for fashion and apparel merchants.
"""

import requests
import json
import time
import csv
import pandas as pd
from typing import List, Dict, Optional, Tuple
from urllib.parse import urlparse, urljoin
import logging
from dataclasses import dataclass
from collections import defaultdict
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


@dataclass
class ProductResult:
    """Store product information"""

    domain: str
    product_url: str
    title: str
    price: Optional[str] = None
    merchant: Optional[str] = None
    image_url: Optional[str] = None


@dataclass
class MerchantInfo:
    """Store merchant information from CSV"""

    name: str
    domain: str
    full_payout: Optional[float]
    rate_type: Optional[str]
    on_shopmy: Optional[str]
    cookie_window: Optional[float]
    niche_classification: Optional[str]
    present_in_shopmy_links: bool
    present_in_mixpanel_insights: bool


class SearchAPIClient:
    """Client for SearchAPI endpoints"""

    def __init__(self, api_key: str):
        self.api_key = api_key
        self.base_url = "https://www.searchapi.io/api/v1"
        self.session = requests.Session()
        self.session.headers.update(
            {"Authorization": f"Bearer {api_key}", "Content-Type": "application/json"}
        )

    def search_google(self, query: str, num_results: int = 10) -> Dict:
        """Regular Google search - useful for finding product pages"""
        endpoint = f"{self.base_url}/search"
        params = {"engine": "google", "q": query, "num": num_results}

        try:
            response = self.session.get(endpoint, params=params)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"Google search error: {e}")
            return {}

    def search_shopping(self, query: str, num_results: int = 20) -> Dict:
        """Search Google Shopping"""
        endpoint = f"{self.base_url}/search"
        params = {
            "engine": "google_shopping",
            "q": query,
            "num": num_results,
            "shopping_condition": "all",
        }

        try:
            response = self.session.get(endpoint, params=params)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"Shopping search error: {e}")
            return {}

    def get_product_details(self, product_id: str) -> Dict:
        """Get detailed product information"""
        endpoint = f"{self.base_url}/search"
        params = {"engine": "google_product", "product_id": product_id}

        try:
            response = self.session.get(endpoint, params=params)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"Product details error: {e}")
            return {}


class FashionProductExtractor:
    """Extract product URLs from fashion and apparel merchants"""

    def __init__(self, api_key: str):
        self.client = SearchAPIClient(api_key)
        self.results = defaultdict(list)
        self.merchants = {}

        # Fashion-specific search terms
        self.fashion_terms = [
            "clothing",
            "apparel",
            "fashion",
            "dress",
            "shirt",
            "pants",
            "shoes",
            "accessories",
            "jacket",
            "coat",
            "bag",
            "jewelry",
        ]

        # Common fashion e-commerce URL patterns
        self.fashion_patterns = [
            "/products/",
            "/product/",
            "/shop/",
            "/store/",
            "/item/",
            "/collections/",
            "/catalog/",
            "/p/",
            "/pd/",
            "/buy/",
        ]

    def load_merchants_csv(self, csv_path: str) -> List[MerchantInfo]:
        """Load merchant data from CSV file"""
        try:
            df = pd.read_csv(csv_path)
            merchants = []

            for _, row in df.iterrows():
                merchant = MerchantInfo(
                    name=str(row.get("name", "")),
                    domain=str(row.get("domain", "")).strip().lower(),
                    full_payout=(
                        row.get("fullPayout")
                        if pd.notna(row.get("fullPayout"))
                        else None
                    ),
                    rate_type=(
                        row.get("rateType") if pd.notna(row.get("rateType")) else None
                    ),
                    on_shopmy=(
                        row.get("On ShopMy?")
                        if pd.notna(row.get("On ShopMy?"))
                        else None
                    ),
                    cookie_window=(
                        row.get("Cookie Window")
                        if pd.notna(row.get("Cookie Window"))
                        else None
                    ),
                    niche_classification=(
                        row.get("niche_classification")
                        if pd.notna(row.get("niche_classification"))
                        else None
                    ),
                    present_in_shopmy_links=bool(
                        row.get("present_in_shopmy_links", False)
                    ),
                    present_in_mixpanel_insights=bool(
                        row.get("present_in_mixpanel_insights", False)
                    ),
                )

                # Clean domain
                merchant.domain = (
                    merchant.domain.replace("https://", "")
                    .replace("http://", "")
                    .strip("/")
                )

                if merchant.domain:
                    merchants.append(merchant)
                    self.merchants[merchant.domain] = merchant

            logger.info(f"Loaded {len(merchants)} merchants from CSV")
            return merchants

        except Exception as e:
            logger.error(f"Error loading CSV: {e}")
            return []

    def extract_url_pattern(
        self, domain: str, product_urls: List[str]
    ) -> Optional[str]:
        """Extract common URL pattern from product URLs"""
        if len(product_urls) < 2:
            return None

        # Parse URLs and find common segments
        parsed_urls = [urlparse(url) for url in product_urls]
        paths = [p.path.split("/") for p in parsed_urls]

        # Find where paths diverge
        common_segments = []
        min_length = min(len(p) for p in paths)

        for i in range(min_length):
            if all(p[i] == paths[0][i] for p in paths if i < len(p)):
                common_segments.append(paths[0][i])
            else:
                break

        # Ensure we have a meaningful pattern
        if common_segments and len(common_segments) > 1:
            pattern = urljoin(f"https://{domain}", "/".join(common_segments))
            return pattern
        return None

    def search_fashion_products(
        self, merchant: MerchantInfo, max_products: int = 2
    ) -> List[ProductResult]:
        """Search for fashion products from a specific merchant"""
        products = []
        domain = merchant.domain

        logger.info(f"Searching for products on {domain} ({merchant.name})")

        # Strategy 1: Direct site search with fashion terms
        fashion_query = f'site:{domain} {" OR ".join(self.fashion_terms[:3])}'
        site_search = self.client.search_google(fashion_query, num_results=15)

        if site_search.get("organic_results"):
            for result in site_search["organic_results"]:
                link = result.get("link", "")
                if domain in link and any(
                    pattern in link.lower() for pattern in self.fashion_patterns
                ):
                    products.append(
                        ProductResult(
                            domain=domain,
                            product_url=link,
                            title=result.get("title", ""),
                            merchant=merchant.name,
                        )
                    )
                    if len(products) >= max_products:
                        break

        # Strategy 2: Search merchant name in Google Shopping
        if len(products) < max_products and merchant.name:
            shopping_search = self.client.search_shopping(
                f'"{merchant.name}" clothing OR apparel', num_results=30
            )

            if shopping_search.get("shopping_results"):
                for result in shopping_search["shopping_results"]:
                    merchant_link = result.get("link", "")
                    source = result.get("source", "").lower()

                    # Check if this is from our merchant
                    if domain in merchant_link or merchant.name.lower() in source:
                        products.append(
                            ProductResult(
                                domain=domain,
                                product_url=merchant_link,
                                title=result.get("title", ""),
                                price=result.get("price"),
                                merchant=merchant.name,
                                image_url=result.get("thumbnail"),
                            )
                        )

                        if len(products) >= max_products:
                            break

        # Strategy 3: Search for specific product pages
        if len(products) < max_products:
            product_search = self.client.search_google(
                f'"{domain}" "add to cart" OR "buy now" OR "shop now"', num_results=20
            )

            if product_search.get("organic_results"):
                for result in product_search["organic_results"]:
                    link = result.get("link", "")
                    if domain in link and link not in [p.product_url for p in products]:
                        # Check if it looks like a product page
                        if any(
                            pattern in link.lower() for pattern in self.fashion_patterns
                        ):
                            products.append(
                                ProductResult(
                                    domain=domain,
                                    product_url=link,
                                    title=result.get("title", ""),
                                    merchant=merchant.name,
                                )
                            )

                            if len(products) >= max_products:
                                break

        return products[:max_products]

    def process_merchants(self, merchants: List[MerchantInfo], output_file: str = None):
        """Process all merchants and extract product URLs"""

        if output_file is None:
            output_file = (
                f'fashion_products_{datetime.now().strftime("%Y%m%d_%H%M%S")}.csv'
            )

        with open(output_file, "w", newline="", encoding="utf-8") as csvfile:
            fieldnames = [
                "merchant_name",
                "domain",
                "product_url_1",
                "product_url_2",
                "url_pattern",
                "status",
                "on_shopmy",
                "niche_classification",
                "product_1_title",
                "product_2_title",
                "product_1_price",
                "product_2_price",
            ]
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()

            for i, merchant in enumerate(merchants):
                logger.info(f"Processing {i+1}/{len(merchants)}: {merchant.domain}")

                try:
                    # Find products
                    products = self.search_fashion_products(merchant)

                    # Extract pattern if we found products
                    product_urls = [p.product_url for p in products]
                    pattern = None
                    if len(product_urls) >= 2:
                        pattern = self.extract_url_pattern(
                            merchant.domain, product_urls
                        )

                    # Write results
                    row = {
                        "merchant_name": merchant.name,
                        "domain": merchant.domain,
                        "product_url_1": (
                            products[0].product_url if len(products) > 0 else ""
                        ),
                        "product_url_2": (
                            products[1].product_url if len(products) > 1 else ""
                        ),
                        "url_pattern": pattern or "",
                        "status": (
                            "success"
                            if len(products) >= 2
                            else "partial" if products else "failed"
                        ),
                        "on_shopmy": merchant.on_shopmy,
                        "niche_classification": merchant.niche_classification,
                        "product_1_title": (
                            products[0].title if len(products) > 0 else ""
                        ),
                        "product_2_title": (
                            products[1].title if len(products) > 1 else ""
                        ),
                        "product_1_price": (
                            products[0].price if len(products) > 0 else ""
                        ),
                        "product_2_price": (
                            products[1].price if len(products) > 1 else ""
                        ),
                    }
                    writer.writerow(row)

                    # Store results
                    self.results[merchant.domain] = products

                    # Rate limiting (adjust based on your SearchAPI plan)
                    time.sleep(2)  # 2 seconds between requests

                except Exception as e:
                    logger.error(f"Error processing {merchant.domain}: {e}")
                    writer.writerow(
                        {
                            "merchant_name": merchant.name,
                            "domain": merchant.domain,
                            "product_url_1": "",
                            "product_url_2": "",
                            "url_pattern": "",
                            "status": "error",
                            "on_shopmy": merchant.on_shopmy,
                            "niche_classification": merchant.niche_classification,
                        }
                    )

        logger.info(f"Results saved to {output_file}")
        self.print_summary()

    def print_summary(self):
        """Print processing summary"""
        total = len(self.results)
        successful = sum(1 for products in self.results.values() if len(products) >= 2)
        partial = sum(1 for products in self.results.values() if 0 < len(products) < 2)
        failed = sum(1 for products in self.results.values() if len(products) == 0)

        logger.info(f"\n=== Processing Summary ===")
        logger.info(f"Total merchants processed: {total}")
        logger.info(
            f"Successful (2+ products): {successful} ({successful/total*100:.1f}%)"
        )
        logger.info(f"Partial (1 product): {partial} ({partial/total*100:.1f}%)")
        logger.info(f"Failed (0 products): {failed} ({failed/total*100:.1f}%)")

        # Analyze by niche
        niche_success = defaultdict(lambda: {"total": 0, "successful": 0})
        for domain, products in self.results.items():
            if domain in self.merchants:
                niche = self.merchants[domain].niche_classification or "Unknown"
                niche_success[niche]["total"] += 1
                if len(products) >= 2:
                    niche_success[niche]["successful"] += 1

        logger.info(f"\n=== Success by Niche ===")
        for niche, stats in niche_success.items():
            success_rate = (
                stats["successful"] / stats["total"] * 100 if stats["total"] > 0 else 0
            )
            logger.info(
                f"{niche}: {stats['successful']}/{stats['total']} ({success_rate:.1f}%)"
            )


# Example usage
if __name__ == "__main__":
    # Configuration
    SEARCHAPI_KEY = "SzAr3r8i3wf5X1ZzSTys3sqe"
    CSV_FILE_PATH = "AllShopMyMerchants7-1-25 - FASHION AND APPAREL.csv"

    # Initialize extractor
    extractor = FashionProductExtractor(SEARCHAPI_KEY)

    # Load merchants from CSV
    merchants = extractor.load_merchants_csv(CSV_FILE_PATH)

    # Process subset for testing (e.g., first 10 merchants)
    test_merchants = merchants[:10]
    logger.info(f"Processing {len(test_merchants)} merchants as a test...")

    # Process all merchants
    extractor.process_merchants(test_merchants, output_file="fashion_products_test.csv")

    # For full processing (be aware of API costs!)
    # extractor.process_merchants(merchants, output_file="fashion_products_full.csv")

    # Alternative: Process only merchants that are on ShopMy
    # shopmy_merchants = [m for m in merchants if m.on_shopmy == 'Yes']
    # extractor.process_merchants(shopmy_merchants, output_file="shopmy_fashion_products.csv")

    # Alternative: Process by niche classification
    # luxury_merchants = [m for m in merchants if m.niche_classification and 'luxury' in m.niche_classification.lower()]
    # extractor.process_merchants(luxury_merchants, output_file="luxury_fashion_products.csv")
