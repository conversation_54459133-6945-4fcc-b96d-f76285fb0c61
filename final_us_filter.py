import csv
import re
from datetime import datetime

def final_us_filter():
    """
    Final refined US merchant filtering with precise detection logic
    """
    print("Running final refined US merchant filtering...")
    
    input_file = "/Users/<USER>/Documents/GitHub/phia/data-pipeline/affiliate_merchants_20250717_162247.csv"
    
    # Create timestamp for output filename
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = f"affiliate_merchants_US_final_{timestamp}.csv"
    
    us_merchants = []
    total_merchants = 0
    
    # Refined US detection criteria
    def is_us_merchant(merchant):
        source = merchant.get('source', '').strip()
        network = merchant.get('network', '').strip()
        name = merchant.get('name', '').strip()
        
        # 1. Explicit US indicators in network/source (highest priority)
        us_network_indicators = ['US', 'USA', 'United States', 'America', 'American']
        
        for indicator in us_network_indicators:
            if indicator in source or indicator in network:
                return True
        
        # 2. Known US-only affiliate networks
        us_only_networks = [
            'ShareASale', 'ShareASale Coupons/Deals'
        ]
        
        if network in us_only_networks:
            return True
        
        # 3. Exclude clearly non-US networks first
        non_us_networks = [
            'Russia', 'Germany', 'UK', 'France', 'Netherlands', 'Belgium', 
            'Denmark', 'Sweden', 'Norway', 'Finland', 'Spain', 'Italy', 
            'Switzerland', 'Austria', 'Canada', 'Australia', 'Brazil',
            'PriceRunner', 'Partner-ads', 'TradeTracker', 'Awin UK',
            'Awin Germany', 'Awin France', 'Addrevenue', 'Daisycon',
            'Commission Factory', 'Adtraction', 'ADCELL', 'Belboon',
            'Effinity', 'Tradedoubler', 'Profitshare', '2Performant'
        ]
        
        network_upper = network.upper()
        for non_us in non_us_networks:
            if non_us.upper() in network_upper:
                return False
        
        # 4. US company patterns in merchant name
        name_upper = name.upper()
        us_company_patterns = [
            r'\bUSA\b', r'\bAMERIC[A|AN]\b', r'\bUNITED STATES\b'
        ]
        
        for pattern in us_company_patterns:
            if re.search(pattern, name_upper):
                return True
        
        # 5. Known major US retailers/brands
        us_major_brands = [
            'WALMART', 'TARGET', 'AMAZON.COM', 'NORDSTROM', 'MACY', 'MACYS',
            'BEST BUY', 'HOME DEPOT', 'COSTCO', 'SEARS', 'KOHLS',
            'JC PENNEY', 'JCPENNEY', 'BARNES & NOBLE', 'STAPLES',
            'OFFICE DEPOT', 'PETCO', 'PETSMART', 'GAMESTOP',
            'AMERICAN EAGLE', 'ABERCROMBIE', 'HOLLISTER',
            'VICTORIA\'S SECRET', 'BATH & BODY WORKS', 'OLD NAVY',
            'GAP', 'BANANA REPUBLIC', 'ANTHROPOLOGIE', 'URBAN OUTFITTERS'
        ]
        
        for brand in us_major_brands:
            if brand in name_upper:
                return True
        
        # 6. .com domains with careful filtering
        if '.com' in name.lower():
            # Only consider .com as US if network doesn't clearly indicate another country
            # AND it's not in a clearly international network
            safe_networks = [
                'CJ', 'RAKUTEN', 'IMPACT', 'FLEXOFFERS', 'AVANTLINK', 
                'PEPPERJAM', 'WEBGAINS', 'GOAFFPRO', 'PARTNERIZE'
            ]
            
            # Check if it's in a network that could be US
            network_base = network.split()[0].upper()  # Get first word of network
            if any(safe_net in network_base for safe_net in safe_networks):
                return True
        
        return False
    
    # Read and filter the CSV
    print("Reading and analyzing merchants with refined logic...")
    with open(input_file, 'r', encoding='utf-8') as infile:
        reader = csv.DictReader(infile)
        
        for row in reader:
            total_merchants += 1
            
            if is_us_merchant(row):
                us_merchants.append(row)
    
    # Write filtered results to new CSV
    print(f"Writing {len(us_merchants)} US merchants to {output_file}...")
    with open(output_file, 'w', newline='', encoding='utf-8') as outfile:
        if us_merchants:
            fieldnames = us_merchants[0].keys()
            writer = csv.DictWriter(outfile, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(us_merchants)
    
    print(f"Successfully created {output_file}")
    
    # Print summary statistics
    print(f"\nFinal Filtering Summary:")
    print(f"Total merchants processed: {total_merchants:,}")
    print(f"US merchants found: {len(us_merchants):,}")
    print(f"Percentage US merchants: {(len(us_merchants)/total_merchants)*100:.1f}%")
    
    # Compare with previous filtering
    original_count = 3295
    improved_count = 4955
    final_count = len(us_merchants)
    
    print(f"Original filtering: {original_count:,}")
    print(f"Improved filtering: {improved_count:,}")
    print(f"Final filtering: {final_count:,}")
    print(f"Net improvement: +{final_count - original_count:,} merchants")
    
    # Show network distribution for US merchants
    print(f"\nFinal US Network distribution:")
    networks = {}
    for merchant in us_merchants:
        network = merchant.get('network', 'Unknown')
        if network not in networks:
            networks[network] = 0
        networks[network] += 1
    
    # Sort networks by count and show top 15
    sorted_networks = sorted(networks.items(), key=lambda x: x[1], reverse=True)
    for network, count in sorted_networks[:15]:
        print(f"  {network:<40} - {count:4d} merchants")
    
    # Verify no obviously non-US networks made it through
    print(f"\nVerification - checking for non-US networks:")
    suspicious_networks = []
    for network, count in sorted_networks:
        network_upper = network.upper()
        if any(country in network_upper for country in ['RUSSIA', 'GERMANY', 'UK', 'FRANCE']):
            suspicious_networks.append((network, count))
    
    if suspicious_networks:
        print("⚠️  Found suspicious networks:")
        for network, count in suspicious_networks:
            print(f"  {network} - {count} merchants")
    else:
        print("✅ No obviously non-US networks found")
    
    return output_file

if __name__ == "__main__":
    output_file = final_us_filter()
    print(f"\n✅ Final US merchants CSV created: {output_file}")
